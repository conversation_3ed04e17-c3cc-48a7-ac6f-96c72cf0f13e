#!/bin/bash

# HVAC-Remix CRM Deployment Testing Script
# Comprehensive testing of all containerized features and integrations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
COMPOSE_FILE="docker-compose.production.yml"
BASE_URL="http://localhost:3000"
GOBACKEND_URL="http://localhost:8080"

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
}

error() {
    echo -e "${RED}❌ $1${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
    FAILED_TESTS+=("$1")
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Test function wrapper
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    log "Testing: $test_name"
    
    if eval "$test_command"; then
        success "$test_name"
    else
        error "$test_name"
    fi
}

# Container health tests
test_container_health() {
    log "🐳 Testing Container Health..."
    
    # Check if containers are running
    run_test "PostgreSQL Container" \
        "docker compose -f $COMPOSE_FILE ps postgres | grep -q 'Up'"
    
    run_test "Redis Container" \
        "docker compose -f $COMPOSE_FILE ps redis | grep -q 'Up'"
    
    run_test "Qdrant Container" \
        "docker compose -f $COMPOSE_FILE ps qdrant | grep -q 'Up'"
    
    run_test "HVAC-Remix Container" \
        "docker compose -f $COMPOSE_FILE ps hvac-remix | grep -q 'Up'"
    
    run_test "Nginx Container" \
        "docker compose -f $COMPOSE_FILE ps nginx | grep -q 'Up'"
}

# Database connectivity tests
test_database_connectivity() {
    log "🗄️  Testing Database Connectivity..."
    
    run_test "PostgreSQL Connection" \
        "docker compose -f $COMPOSE_FILE exec -T postgres pg_isready -U hvacdb -d hvacdb"
    
    run_test "Redis Connection" \
        "docker compose -f $COMPOSE_FILE exec -T redis redis-cli ping | grep -q PONG"
    
    run_test "Database Schema" \
        "docker compose -f $COMPOSE_FILE exec -T postgres psql -U hvacdb -d hvacdb -c '\dt' | grep -q customers"
}

# Application health tests
test_application_health() {
    log "🚀 Testing Application Health..."
    
    # Wait for application to be ready
    local timeout=60
    while ! curl -f "$BASE_URL/health" &> /dev/null; do
        if [ $timeout -le 0 ]; then
            error "Application failed to start within timeout"
            return 1
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    run_test "Application Health Endpoint" \
        "curl -f $BASE_URL/health"
    
    run_test "Application Homepage" \
        "curl -f $BASE_URL/ | grep -q 'HVAC'"
    
    run_test "API Health Check" \
        "curl -f $BASE_URL/api/health"
}

# GoBackend integration tests
test_gobackend_integration() {
    log "🔗 Testing GoBackend-Kratos Integration..."
    
    # Test if GoBackend is accessible from HVAC-Remix container
    run_test "GoBackend Connectivity from Container" \
        "docker compose -f $COMPOSE_FILE exec -T hvac-remix curl -f http://gobackend-kratos:8080/health"
    
    run_test "GoBackend Monitoring API" \
        "curl -f $BASE_URL/api/gobackend/monitoring"
    
    # Test connection pool stats
    run_test "Connection Pool Statistics" \
        "curl -f $BASE_URL/api/gobackend/monitoring | grep -q 'poolStats'"
    
    # Test cache statistics
    run_test "Cache Statistics" \
        "curl -f $BASE_URL/api/gobackend/monitoring | grep -q 'cacheStats'"
}

# Sprint 1 features tests (Enhanced GoBackend Integration)
test_sprint1_features() {
    log "🎯 Testing Sprint 1 Features (Enhanced GoBackend Integration)..."
    
    # Test connection pooling
    run_test "Connection Pool Functionality" \
        "curl -f $BASE_URL/api/gobackend/monitoring | jq -e '.poolStats.total > 0'"
    
    # Test caching system
    run_test "Cache System Functionality" \
        "curl -f $BASE_URL/api/gobackend/monitoring | jq -e '.cacheStats'"
    
    # Test metrics collection
    run_test "Metrics Collection" \
        "curl -f $BASE_URL/api/gobackend/monitoring | jq -e '.metrics.requestCount >= 0'"
    
    # Test health monitoring
    run_test "Health Monitoring System" \
        "curl -f $BASE_URL/api/gobackend/monitoring | jq -e '.systemHealth.status'"
}

# Sprint 2 features tests (Advanced CRM Features)
test_sprint2_features() {
    log "📊 Testing Sprint 2 Features (Advanced CRM Features)..."
    
    # Test analytics dashboard
    run_test "Analytics Dashboard API" \
        "curl -f $BASE_URL/api/analytics/dashboard"
    
    # Test report preview functionality
    run_test "Report Preview API" \
        "curl -X POST -H 'Content-Type: application/json' -d '{\"selectedFields\":[{\"id\":\"customer_name\",\"name\":\"customer.name\",\"label\":\"Customer Name\",\"type\":\"string\",\"table\":\"customers\"}],\"filters\":[]}' $BASE_URL/api/reports/preview"
    
    # Test export functionality
    run_test "Data Export API" \
        "curl -X POST -H 'Content-Type: application/json' -d '{\"reportConfig\":{\"name\":\"Test Report\",\"selectedFields\":[{\"name\":\"customer.name\",\"label\":\"Customer Name\",\"type\":\"string\"}]},\"format\":\"csv\",\"data\":[{\"customer.name\":\"Test Customer\"}]}' $BASE_URL/api/reports/export"
    
    # Test customer portal endpoints
    run_test "Customer Portal API Structure" \
        "curl -f $BASE_URL/api/customers/1/profile || curl -f $BASE_URL/api/customers/profile"
}

# Performance tests
test_performance() {
    log "⚡ Testing Performance..."
    
    # Test response times
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "$BASE_URL/health")
    if (( $(echo "$response_time < 1.0" | bc -l) )); then
        success "Response Time Under 1 Second ($response_time s)"
    else
        error "Response Time Too Slow ($response_time s)"
    fi
    
    # Test concurrent requests
    run_test "Concurrent Request Handling" \
        "for i in {1..10}; do curl -f $BASE_URL/health & done; wait"
    
    # Test memory usage
    local memory_usage=$(docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}" | grep hvac-remix | awk '{print $2}' | cut -d'/' -f1)
    log "Memory Usage: $memory_usage"
}

# Security tests
test_security() {
    log "🔒 Testing Security..."
    
    # Test HTTPS redirect (if nginx is configured)
    run_test "Security Headers Present" \
        "curl -I $BASE_URL/ | grep -q 'X-Content-Type-Options'"
    
    # Test that sensitive endpoints require authentication
    run_test "Protected Endpoints" \
        "curl -f $BASE_URL/api/admin/users && false || true"
    
    # Test environment variable security
    run_test "Environment Variables Not Exposed" \
        "! curl -f $BASE_URL/api/env"
}

# Data persistence tests
test_data_persistence() {
    log "💾 Testing Data Persistence..."
    
    # Test volume mounts
    run_test "PostgreSQL Data Volume" \
        "docker volume ls | grep -q postgres-data"
    
    run_test "Redis Data Volume" \
        "docker volume ls | grep -q redis-data"
    
    run_test "Application Logs Volume" \
        "docker volume ls | grep -q hvac-logs"
    
    # Test log file creation
    run_test "Application Log Files" \
        "docker compose -f $COMPOSE_FILE exec hvac-remix ls -la /app/logs/ | grep -q '.log' || true"
}

# Monitoring tests
test_monitoring() {
    log "📈 Testing Monitoring..."
    
    # Test Prometheus (if running)
    if docker compose -f $COMPOSE_FILE ps prometheus | grep -q "Up"; then
        run_test "Prometheus Metrics" \
            "curl -f http://localhost:9090/metrics"
    else
        warning "Prometheus not running - skipping metrics test"
    fi
    
    # Test Grafana (if running)
    if docker compose -f $COMPOSE_FILE ps grafana | grep -q "Up"; then
        run_test "Grafana Dashboard" \
            "curl -f http://localhost:3001/login"
    else
        warning "Grafana not running - skipping dashboard test"
    fi
}

# Backup and recovery tests
test_backup_recovery() {
    log "🔄 Testing Backup & Recovery..."
    
    # Test backup script exists and is executable
    run_test "Backup Script Executable" \
        "test -x ./deploy.sh"
    
    # Test backup directory creation
    run_test "Backup Directory Creation" \
        "mkdir -p ./test-backups && rmdir ./test-backups"
    
    # Test database dump capability
    run_test "Database Dump Capability" \
        "docker compose -f $COMPOSE_FILE exec -T postgres pg_dump --help | grep -q 'pg_dump'"
}

# Integration workflow tests
test_integration_workflows() {
    log "🔄 Testing Integration Workflows..."
    
    # Test full request cycle: Frontend -> GoBackend -> Database
    run_test "End-to-End Request Flow" \
        "curl -f $BASE_URL/api/gobackend/monitoring | jq -e '.systemHealth'"
    
    # Test caching workflow
    run_test "Cache Hit/Miss Workflow" \
        "curl -f $BASE_URL/api/gobackend/monitoring | jq -e '.cacheStats.hits >= 0 and .cacheStats.misses >= 0'"
    
    # Test error handling workflow
    run_test "Error Handling Workflow" \
        "curl -f $BASE_URL/api/nonexistent-endpoint && false || true"
}

# Generate test report
generate_report() {
    log "📋 Generating Test Report..."
    
    local total_tests=$((TESTS_PASSED + TESTS_FAILED))
    local success_rate=$(( TESTS_PASSED * 100 / total_tests ))
    
    echo ""
    echo "=========================================="
    echo "🧪 HVAC-Remix CRM Deployment Test Report"
    echo "=========================================="
    echo ""
    echo "📊 Test Summary:"
    echo "  Total Tests: $total_tests"
    echo "  Passed: $TESTS_PASSED ✅"
    echo "  Failed: $TESTS_FAILED ❌"
    echo "  Success Rate: $success_rate%"
    echo ""
    
    if [ $TESTS_FAILED -gt 0 ]; then
        echo "❌ Failed Tests:"
        for test in "${FAILED_TESTS[@]}"; do
            echo "  - $test"
        done
        echo ""
    fi
    
    if [ $success_rate -ge 90 ]; then
        echo "🎉 Deployment Status: EXCELLENT ($success_rate%)"
    elif [ $success_rate -ge 80 ]; then
        echo "✅ Deployment Status: GOOD ($success_rate%)"
    elif [ $success_rate -ge 70 ]; then
        echo "⚠️  Deployment Status: ACCEPTABLE ($success_rate%)"
    else
        echo "❌ Deployment Status: NEEDS ATTENTION ($success_rate%)"
    fi
    
    echo ""
    echo "🔗 Useful Commands:"
    echo "  View logs: docker compose -f $COMPOSE_FILE logs -f"
    echo "  Check status: docker compose -f $COMPOSE_FILE ps"
    echo "  Restart services: docker compose -f $COMPOSE_FILE restart"
    echo ""
}

# Main test execution
main() {
    log "🧪 Starting HVAC-Remix CRM Deployment Tests"
    echo ""
    
    # Run all test suites
    test_container_health
    test_database_connectivity
    test_application_health
    test_gobackend_integration
    test_sprint1_features
    test_sprint2_features
    test_performance
    test_security
    test_data_persistence
    test_monitoring
    test_backup_recovery
    test_integration_workflows
    
    # Generate final report
    generate_report
    
    # Exit with appropriate code
    if [ $TESTS_FAILED -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Handle command line arguments
case "${1:-all}" in
    "all")
        main
        ;;
    "health")
        test_container_health
        test_application_health
        ;;
    "integration")
        test_gobackend_integration
        test_integration_workflows
        ;;
    "features")
        test_sprint1_features
        test_sprint2_features
        ;;
    "performance")
        test_performance
        ;;
    "security")
        test_security
        ;;
    *)
        echo "Usage: $0 {all|health|integration|features|performance|security}"
        echo ""
        echo "Test Suites:"
        echo "  all         - Run all tests"
        echo "  health      - Container and application health"
        echo "  integration - GoBackend integration tests"
        echo "  features    - Sprint 1 & 2 feature tests"
        echo "  performance - Performance and load tests"
        echo "  security    - Security and access tests"
        exit 1
        ;;
esac

#!/bin/sh
set -e

# Enhanced HVAC-Remix CRM Docker Entrypoint
echo "🚀 Starting HVAC-Remix CRM Container..."

# Function to wait for service
wait_for_service() {
    host=$1
    port=$2
    service_name=$3
    max_attempts=30
    attempt=1

    echo "⏳ Waiting for $service_name to be ready at $host:$port..."

    while ! nc -z "$host" "$port" 2>/dev/null; do
        if [ $attempt -eq $max_attempts ]; then
            echo "❌ Failed to connect to $service_name after $max_attempts attempts"
            exit 1
        fi
        echo "   Attempt $attempt/$max_attempts - waiting for $service_name..."
        sleep 2
        attempt=$((attempt + 1))
    done

    echo "✅ $service_name is ready!"
}

# Wait for required services
if [ -n "$DATABASE_HOST" ] && [ -n "$DATABASE_PORT" ]; then
    wait_for_service "$DATABASE_HOST" "$DATABASE_PORT" "PostgreSQL Database"
fi

if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ]; then
    wait_for_service "$REDIS_HOST" "$REDIS_PORT" "Redis Cache"
fi

if [ -n "$GOBACKEND_HOST" ] && [ -n "$GOBACKEND_PORT" ]; then
    wait_for_service "$GOBACKEND_HOST" "$GOBACKEND_PORT" "GoBackend-Kratos"
fi

# Create symbolic links for the app directory in the build directory
echo "🔗 Setting up application links..."
mkdir -p /app/build/app
ln -sf /app/app/* /app/build/app/ 2>/dev/null || true
# Ensure db.server.ts is properly linked
ln -sf /app/app/db.server.ts /app/build/app/db.server.ts 2>/dev/null || true
# Ensure utils/db.server.ts is properly linked
mkdir -p /app/build/app/utils
ln -sf /app/app/utils/db.server.ts /app/build/app/utils/db.server.ts 2>/dev/null || true

# Database operations
if [ -n "$DATABASE_URL" ]; then
    echo "🔄 Running database migrations..."
    npx prisma migrate deploy || {
        echo "❌ Database migration failed"
        exit 1
    }
fi

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Create necessary directories
mkdir -p /app/logs /app/tmp /app/uploads
echo "📁 Created application directories"

# Start the application
echo "🎯 Starting HVAC-Remix CRM Application..."
echo "   Environment: $NODE_ENV"
echo "   Port: $PORT"

# Start the application
exec "$@"

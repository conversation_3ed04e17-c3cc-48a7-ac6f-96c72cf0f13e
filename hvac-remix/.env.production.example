# HVAC-Remix CRM Production Environment Configuration
# Copy this file to .env.production and update with your actual values

# Application Settings
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
DOMAIN_NAME=your-domain.com

# Database Configuration
DATABASE_URL=******************************************************/hvacdb
POSTGRES_PASSWORD=your-secure-password

# Redis Configuration
REDIS_URL=redis://redis:6379

# Vector Database Configuration
QDRANT_URL=http://qdrant:6333

# GoBackend-Kratos Integration
GOBACKEND_URL=http://gobackend-kratos:8080
GOBACKEND_API_KEY=your-gobackend-api-key

# Security Keys (Generate secure random strings)
SESSION_SECRET=your-session-secret-key-min-32-chars
ENCRYPTION_KEY=your-encryption-key-exactly-32-chars
JWT_SECRET=your-jwt-secret-key-min-32-chars

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# AI Model Configuration
OPENAI_API_KEY=your-openai-api-key
BIELIK_API_URL=http://localhost:8877
GEMMA_API_URL=http://localhost:8878
GEMMA_4B_API_URL=http://localhost:8879

# LM Studio Configuration
LM_STUDIO_URL=http://host.docker.internal:1234

# Email Configuration (for scheduled reports)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png,gif

# Monitoring Configuration
LOG_LEVEL=info
ENABLE_METRICS=true
GRAFANA_PASSWORD=your-grafana-password

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/letsencrypt/live/your-domain.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/your-domain.com/privkey.pem

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Performance Configuration
CACHE_TTL=300000  # 5 minutes
CONNECTION_POOL_SIZE=10
MAX_CONCURRENT_REQUESTS=100

# Feature Flags
ENABLE_SCHEDULED_REPORTS=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_CUSTOMER_PORTAL=true
ENABLE_EXPORT_FEATURES=true

# External Integrations
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# Notification Configuration
SLACK_WEBHOOK_URL=your-slack-webhook-url
DISCORD_WEBHOOK_URL=your-discord-webhook-url

# Rate Limiting
RATE_LIMIT_WINDOW=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=https://your-domain.com
CORS_CREDENTIALS=true

#!/bin/bash

# HVAC-Remix CRM Production Deployment Script
# Automated deployment with health checks and rollback capabilities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.production.yml"
ENV_FILE=".env.production"
BACKUP_DIR="./backups"
LOG_FILE="./deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! docker compose version &> /dev/null; then
        error "Docker Compose v2 is not installed"
        exit 1
    fi
    
    # Check environment file
    if [ ! -f "$ENV_FILE" ]; then
        error "Environment file $ENV_FILE not found"
        echo "Please copy .env.production.example to .env.production and configure it"
        exit 1
    fi
    
    # Check compose file
    if [ ! -f "$COMPOSE_FILE" ]; then
        error "Docker Compose file $COMPOSE_FILE not found"
        exit 1
    fi
    
    success "Prerequisites check passed"
}

# Validate environment configuration
validate_environment() {
    log "Validating environment configuration..."
    
    # Source environment file
    set -a
    source "$ENV_FILE"
    set +a
    
    # Check required variables
    required_vars=(
        "DOMAIN_NAME"
        "POSTGRES_PASSWORD"
        "SESSION_SECRET"
        "ENCRYPTION_KEY"
        "JWT_SECRET"
        "DATABASE_URL"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    # Validate key lengths
    if [ ${#SESSION_SECRET} -lt 32 ]; then
        error "SESSION_SECRET must be at least 32 characters long"
        exit 1
    fi
    
    if [ ${#ENCRYPTION_KEY} -ne 32 ]; then
        error "ENCRYPTION_KEY must be exactly 32 characters long"
        exit 1
    fi
    
    success "Environment validation passed"
}

# Create backup
create_backup() {
    log "Creating backup before deployment..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup database if running
    if docker compose -f "$COMPOSE_FILE" ps postgres | grep -q "Up"; then
        log "Backing up database..."
        docker compose -f "$COMPOSE_FILE" exec -T postgres \
            pg_dump -U hvacdb hvacdb | gzip > "$BACKUP_DIR/hvacdb_$(date +%Y%m%d_%H%M%S).sql.gz"
        success "Database backup created"
    fi
    
    # Backup volumes
    log "Backing up volumes..."
    for volume in postgres-data redis-data qdrant-data; do
        if docker volume ls | grep -q "hvac-remix_$volume"; then
            docker run --rm \
                -v "hvac-remix_$volume:/data" \
                -v "$(pwd)/$BACKUP_DIR:/backup" \
                alpine tar czf "/backup/${volume}_$(date +%Y%m%d_%H%M%S).tar.gz" -C /data .
        fi
    done
    
    success "Backup completed"
}

# Build and deploy
deploy() {
    log "Starting deployment..."
    
    # Pull latest images
    log "Pulling latest images..."
    docker compose -f "$COMPOSE_FILE" pull
    
    # Build application
    log "Building HVAC-Remix application..."
    docker compose -f "$COMPOSE_FILE" build --no-cache hvac-remix
    
    # Start infrastructure services first
    log "Starting infrastructure services..."
    docker compose -f "$COMPOSE_FILE" up -d postgres redis qdrant
    
    # Wait for database to be ready
    log "Waiting for database to be ready..."
    timeout=60
    while ! docker compose -f "$COMPOSE_FILE" exec postgres pg_isready -U hvacdb -d hvacdb &> /dev/null; do
        if [ $timeout -le 0 ]; then
            error "Database failed to start within timeout"
            exit 1
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    success "Database is ready"
    
    # Run database migrations
    log "Running database migrations..."
    docker compose -f "$COMPOSE_FILE" run --rm hvac-remix npx prisma migrate deploy
    
    # Start application services
    log "Starting application services..."
    docker compose -f "$COMPOSE_FILE" up -d
    
    success "Deployment completed"
}

# Health checks
health_check() {
    log "Performing health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check service status
    services=("postgres" "redis" "qdrant" "hvac-remix" "gobackend-kratos")
    
    for service in "${services[@]}"; do
        if docker compose -f "$COMPOSE_FILE" ps "$service" | grep -q "Up"; then
            success "$service is running"
        else
            error "$service is not running"
            docker compose -f "$COMPOSE_FILE" logs "$service"
            return 1
        fi
    done
    
    # Test application health endpoint
    log "Testing application health endpoint..."
    timeout=60
    while ! curl -f http://localhost:3000/health &> /dev/null; do
        if [ $timeout -le 0 ]; then
            error "Application health check failed"
            return 1
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    success "Application health check passed"
    
    # Test GoBackend integration
    log "Testing GoBackend integration..."
    if docker compose -f "$COMPOSE_FILE" exec hvac-remix curl -f http://gobackend-kratos:8080/health &> /dev/null; then
        success "GoBackend integration is working"
    else
        warning "GoBackend integration test failed (may be expected if GoBackend is not running)"
    fi
    
    success "Health checks completed"
}

# Rollback function
rollback() {
    error "Deployment failed. Starting rollback..."
    
    # Stop current deployment
    docker compose -f "$COMPOSE_FILE" down
    
    # Restore from backup if available
    latest_backup=$(ls -t "$BACKUP_DIR"/hvacdb_*.sql.gz 2>/dev/null | head -n1)
    if [ -n "$latest_backup" ]; then
        log "Restoring database from backup: $latest_backup"
        docker compose -f "$COMPOSE_FILE" up -d postgres
        sleep 10
        zcat "$latest_backup" | docker compose -f "$COMPOSE_FILE" exec -T postgres psql -U hvacdb -d hvacdb
    fi
    
    error "Rollback completed. Please check logs and fix issues before retrying deployment."
    exit 1
}

# Cleanup old backups
cleanup_backups() {
    log "Cleaning up old backups..."
    find "$BACKUP_DIR" -name "*.sql.gz" -mtime +7 -delete
    find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete
    success "Old backups cleaned up"
}

# Show deployment status
show_status() {
    log "Deployment Status:"
    echo ""
    docker compose -f "$COMPOSE_FILE" ps
    echo ""
    log "Service URLs:"
    echo "  🌐 Application: https://${DOMAIN_NAME:-localhost}"
    echo "  📊 Grafana: https://${DOMAIN_NAME:-localhost}:3001"
    echo "  📈 Prometheus: https://${DOMAIN_NAME:-localhost}:9090"
    echo ""
    log "Useful Commands:"
    echo "  📋 View logs: docker compose -f $COMPOSE_FILE logs -f"
    echo "  🔄 Restart: docker compose -f $COMPOSE_FILE restart"
    echo "  🛑 Stop: docker compose -f $COMPOSE_FILE down"
    echo "  📊 Stats: docker stats"
}

# Main deployment process
main() {
    log "🚀 Starting HVAC-Remix CRM Production Deployment"
    
    # Trap errors for rollback
    trap rollback ERR
    
    check_prerequisites
    validate_environment
    create_backup
    deploy
    
    # Remove error trap for health checks
    trap - ERR
    
    if health_check; then
        cleanup_backups
        success "🎉 Deployment completed successfully!"
        show_status
    else
        rollback
    fi
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "health")
        health_check
        ;;
    "backup")
        create_backup
        ;;
    "status")
        show_status
        ;;
    "logs")
        docker compose -f "$COMPOSE_FILE" logs -f "${2:-}"
        ;;
    "stop")
        log "Stopping HVAC-Remix CRM..."
        docker compose -f "$COMPOSE_FILE" down
        success "Services stopped"
        ;;
    "restart")
        log "Restarting HVAC-Remix CRM..."
        docker compose -f "$COMPOSE_FILE" restart
        success "Services restarted"
        ;;
    *)
        echo "Usage: $0 {deploy|health|backup|status|logs|stop|restart}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Full deployment with health checks"
        echo "  health  - Run health checks only"
        echo "  backup  - Create backup only"
        echo "  status  - Show deployment status"
        echo "  logs    - Show service logs"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        exit 1
        ;;
esac

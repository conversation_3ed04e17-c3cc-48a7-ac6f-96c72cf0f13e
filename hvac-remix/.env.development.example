# HVAC-Remix CRM Development Environment Configuration
# Copy this file to .env.development and update with your values

# Application Settings
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
DOMAIN_NAME=localhost

# Database Configuration
DATABASE_URL=postgresql://hvacdb:devpassword@localhost:5433/hvacdb
POSTGRES_PASSWORD=devpassword

# Redis Configuration
REDIS_URL=redis://localhost:6380

# Vector Database Configuration
QDRANT_URL=http://localhost:6335

# GoBackend-Kratos Integration
GOBACKEND_URL=http://localhost:8081
GOBACKEND_API_KEY=dev-api-key

# Security Keys (Development - Not for production!)
SESSION_SECRET=dev-session-secret-key-for-development
ENCRYPTION_KEY=dev-encryption-key-32-chars-long
JWT_SECRET=dev-jwt-secret-key-for-development

# Supabase Configuration (Development)
SUPABASE_URL=https://your-dev-project.supabase.co
SUPABASE_ANON_KEY=your-dev-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-dev-supabase-service-role-key

# AI Model Configuration
OPENAI_API_KEY=your-openai-api-key
BIELIK_API_URL=http://localhost:8877
GEMMA_API_URL=http://localhost:8878
GEMMA_4B_API_URL=http://localhost:8879

# LM Studio Configuration
LM_STUDIO_URL=http://localhost:1234

# Email Configuration (Development - Use Mailhog)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=dev
SMTP_PASSWORD=dev
SMTP_FROM=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png,gif

# Monitoring Configuration
LOG_LEVEL=debug
ENABLE_METRICS=true

# Performance Configuration
CACHE_TTL=60000  # 1 minute for development
CONNECTION_POOL_SIZE=5
MAX_CONCURRENT_REQUESTS=50

# Feature Flags
ENABLE_SCHEDULED_REPORTS=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_CUSTOMER_PORTAL=true
ENABLE_EXPORT_FEATURES=true

# Development Tools
CHOKIDAR_USEPOLLING=true
ENABLE_HOT_RELOAD=true
ENABLE_DEBUG_LOGS=true

# External Integrations (Development)
GOOGLE_MAPS_API_KEY=your-dev-google-maps-api-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-test-key
STRIPE_SECRET_KEY=sk_test_your-stripe-test-key

# Rate Limiting (Relaxed for development)
RATE_LIMIT_WINDOW=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=1000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Database Admin Tools
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=devpassword

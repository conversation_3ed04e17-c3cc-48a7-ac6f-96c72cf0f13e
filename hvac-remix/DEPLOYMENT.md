# HVAC-Remix CRM - Production Deployment Guide

## 🚀 Complete Docker Deployment Documentation

This guide provides comprehensive instructions for deploying the HVAC-Remix CRM system in production using Docker containers with full integration to GoBackend-Kratos.

---

## 📋 Prerequisites

### System Requirements
- **Docker**: Version 20.10+ with Docker Compose v2
- **Server**: Linux (Ubuntu 20.04+ recommended)
- **Memory**: Minimum 8GB RAM (16GB+ recommended)
- **Storage**: 50GB+ available disk space
- **Network**: Static IP address and domain name

### Required Services
- **GoBackend-Kratos**: Must be running and accessible
- **LM Studio**: For AI model integration (optional)
- **SSL Certificate**: For HTTPS (Let's Encrypt recommended)

---

## 🔧 Quick Start Deployment

### 1. Clone and Setup
```bash
# Clone the repository
git clone https://github.com/hvac-crm/hvac-remix.git
cd hvac-remix

# Copy environment configuration
cp .env.production.example .env.production

# Edit environment variables
nano .env.production
```

### 2. Configure Environment Variables
Update `.env.production` with your actual values:

```bash
# Essential Configuration
DOMAIN_NAME=your-domain.com
POSTGRES_PASSWORD=your-secure-password
SESSION_SECRET=your-session-secret-key-min-32-chars
ENCRYPTION_KEY=your-encryption-key-exactly-32-chars
JWT_SECRET=your-jwt-secret-key-min-32-chars

# Database URLs
DATABASE_URL=******************************************************/hvacdb
REDIS_URL=redis://redis:6379
QDRANT_URL=http://qdrant:6333
GOBACKEND_URL=http://gobackend-kratos:8080

# AI Integration
OPENAI_API_KEY=your-openai-api-key
LM_STUDIO_URL=http://host.docker.internal:1234

# Email Configuration
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
```

### 3. Deploy Production Stack
```bash
# Build and start all services
docker-compose -f docker-compose.production.yml up -d

# Check service status
docker-compose -f docker-compose.production.yml ps

# View logs
docker-compose -f docker-compose.production.yml logs -f hvac-remix
```

---

## 🏗️ Architecture Overview

### Service Components
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  HVAC-Remix App │    │ GoBackend-Kratos│
│   (Port 80/443) │────│   (Port 3000)   │────│   (Port 8080)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   PostgreSQL    │              │
         └──────────────│   (Port 5432)   │──────────────┘
                        └─────────────────┘
                                 │
                    ┌─────────────────┐    ┌─────────────────┐
                    │      Redis      │    │     Qdrant      │
                    │   (Port 6379)   │    │   (Port 6333)   │
                    └─────────────────┘    └─────────────────┘
```

### Network Configuration
- **Frontend**: HVAC-Remix React/Remix application
- **Backend**: GoBackend-Kratos integration layer
- **Database**: PostgreSQL with optimized configuration
- **Cache**: Redis for session and application caching
- **Vector DB**: Qdrant for AI/ML features
- **Proxy**: Nginx with SSL termination and load balancing

---

## 🔒 Security Configuration

### SSL/TLS Setup
```bash
# Install Certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal setup
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Firewall Configuration
```bash
# Configure UFW firewall
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### Environment Security
- Use strong, unique passwords for all services
- Rotate secrets regularly
- Enable database encryption at rest
- Configure proper backup encryption

---

## 📊 Monitoring & Health Checks

### Service Health Monitoring
```bash
# Check all service health
docker-compose -f docker-compose.production.yml exec hvac-remix curl -f http://localhost:3000/health

# Monitor resource usage
docker stats

# View service logs
docker-compose -f docker-compose.production.yml logs -f --tail=100
```

### Grafana Dashboard Access
- **URL**: `https://your-domain.com:3001`
- **Username**: `admin`
- **Password**: Set in `GRAFANA_PASSWORD` environment variable

### Prometheus Metrics
- **URL**: `https://your-domain.com:9090`
- **Metrics**: Application performance, database stats, system resources

---

## 🔄 Backup & Recovery

### Database Backup
```bash
# Create backup script
cat > backup-database.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

docker-compose -f docker-compose.production.yml exec -T postgres \
  pg_dump -U hvacdb hvacdb | gzip > $BACKUP_DIR/hvacdb_$DATE.sql.gz

# Keep only last 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
EOF

chmod +x backup-database.sh

# Add to crontab for daily backups
echo "0 2 * * * /path/to/backup-database.sh" | crontab -
```

### Volume Backup
```bash
# Backup all Docker volumes
docker run --rm -v hvac-remix_postgres-data:/data -v $(pwd):/backup \
  alpine tar czf /backup/postgres-data-backup.tar.gz -C /data .

docker run --rm -v hvac-remix_redis-data:/data -v $(pwd):/backup \
  alpine tar czf /backup/redis-data-backup.tar.gz -C /data .
```

---

## 🚨 Troubleshooting Guide

### Common Issues

#### 1. Container Won't Start
```bash
# Check container logs
docker-compose -f docker-compose.production.yml logs hvac-remix

# Check resource usage
docker system df
docker system prune  # Clean up if needed
```

#### 2. Database Connection Issues
```bash
# Test database connectivity
docker-compose -f docker-compose.production.yml exec hvac-remix \
  npx prisma db push

# Check database logs
docker-compose -f docker-compose.production.yml logs postgres
```

#### 3. GoBackend Integration Issues
```bash
# Test GoBackend connectivity
docker-compose -f docker-compose.production.yml exec hvac-remix \
  curl -f http://gobackend-kratos:8080/health

# Check network connectivity
docker network ls
docker network inspect hvac-remix_hvac-network
```

#### 4. Performance Issues
```bash
# Monitor resource usage
docker stats --no-stream

# Check application metrics
curl -f https://your-domain.com/api/gobackend/monitoring

# Optimize database
docker-compose -f docker-compose.production.yml exec postgres \
  psql -U hvacdb -d hvacdb -c "VACUUM ANALYZE;"
```

### Log Analysis
```bash
# Application logs
docker-compose -f docker-compose.production.yml logs -f hvac-remix

# Database logs
docker-compose -f docker-compose.production.yml logs -f postgres

# Nginx access logs
docker-compose -f docker-compose.production.yml logs -f nginx

# System logs
journalctl -u docker.service -f
```

---

## 🔄 Updates & Maintenance

### Application Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart services
docker-compose -f docker-compose.production.yml build --no-cache
docker-compose -f docker-compose.production.yml up -d

# Run database migrations
docker-compose -f docker-compose.production.yml exec hvac-remix \
  npx prisma migrate deploy
```

### System Maintenance
```bash
# Clean up Docker resources
docker system prune -a

# Update system packages
sudo apt update && sudo apt upgrade -y

# Restart services if needed
docker-compose -f docker-compose.production.yml restart
```

---

## 📈 Performance Optimization

### Database Optimization
```sql
-- Connect to database and run optimization queries
-- docker-compose exec postgres psql -U hvacdb -d hvacdb

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM customers WHERE email = '<EMAIL>';

-- Update statistics
ANALYZE;

-- Vacuum database
VACUUM ANALYZE;
```

### Application Optimization
- **Connection Pooling**: Already configured in GoBackend bridge
- **Caching**: Redis caching enabled with 5-minute TTL
- **CDN**: Consider adding CloudFlare for static assets
- **Database Indexing**: Ensure proper indexes on frequently queried columns

---

## 🎯 Production Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] SSL certificates obtained
- [ ] Firewall rules configured
- [ ] Backup strategy implemented
- [ ] Monitoring setup completed

### Post-Deployment
- [ ] All services healthy
- [ ] Database migrations applied
- [ ] SSL certificate working
- [ ] Monitoring dashboards accessible
- [ ] Backup scripts tested
- [ ] Performance metrics baseline established

### Ongoing Maintenance
- [ ] Regular security updates
- [ ] Database maintenance
- [ ] Log rotation configured
- [ ] Backup verification
- [ ] Performance monitoring
- [ ] SSL certificate renewal

---

## 📞 Support & Resources

### Documentation Links
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [Nginx Configuration](https://nginx.org/en/docs/)
- [PostgreSQL Tuning](https://wiki.postgresql.org/wiki/Tuning_Your_PostgreSQL_Server)
- [Redis Configuration](https://redis.io/documentation)

### Emergency Contacts
- **System Administrator**: <EMAIL>
- **Database Administrator**: <EMAIL>
- **Development Team**: <EMAIL>

---

**Last Updated**: January 2025  
**Version**: 2.0  
**Deployment Status**: Production Ready ✅

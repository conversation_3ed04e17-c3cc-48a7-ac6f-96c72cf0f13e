/**
 * GoBackend Monitoring API Route
 * 
 * Provides real-time monitoring data for the GoBackend-Kratos integration
 * Returns metrics, cache stats, connection pool stats, and system health
 */

import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { goBackendBridge } from '~/services/gobackend-bridge.server';

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Get all monitoring data from the enhanced bridge
    const [metrics, cacheStats, poolStats, systemHealth] = await Promise.all([
      goBackendBridge.getMetrics(),
      goBackendBridge.getCacheStats(),
      goBackendBridge.getConnectionPoolStats(),
      goBackendBridge.checkHealth(),
    ]);

    const monitoringData = {
      metrics,
      cacheStats,
      poolStats,
      systemHealth,
      lastUpdated: new Date(),
    };

    return json(monitoringData);
  } catch (error) {
    console.error('Failed to get monitoring data:', error);
    
    // Return error response with basic structure
    return json(
      {
        metrics: {
          requestCount: 0,
          errorCount: 0,
          totalLatency: 0,
          averageLatency: 0,
          errorRate: 0,
          healthCheckFailures: 0,
        },
        cacheStats: {
          size: 0,
          hits: 0,
          misses: 0,
          hitRate: 0,
          memoryUsage: 0,
        },
        poolStats: {
          total: 0,
          active: 0,
          idle: 0,
          waiting: 0,
        },
        systemHealth: {
          status: 'unhealthy' as const,
          services: {},
          latency: 0,
        },
        lastUpdated: new Date(),
        error: 'Failed to retrieve monitoring data',
      },
      { status: 500 }
    );
  }
}

// POST endpoint for cache management operations
export async function action({ request }: LoaderFunctionArgs) {
  const formData = await request.formData();
  const action = formData.get('action') as string;

  try {
    switch (action) {
      case 'clear-cache':
        await goBackendBridge.clearCache();
        return json({ success: true, message: 'Cache cleared successfully' });

      case 'invalidate-pattern':
        const pattern = formData.get('pattern') as string;
        if (!pattern) {
          return json({ success: false, message: 'Pattern is required' }, { status: 400 });
        }
        const invalidatedCount = await goBackendBridge.invalidateCachePattern(pattern);
        return json({ 
          success: true, 
          message: `Invalidated ${invalidatedCount} cache entries` 
        });

      default:
        return json({ success: false, message: 'Unknown action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Cache management error:', error);
    return json(
      { success: false, message: 'Cache management operation failed' },
      { status: 500 }
    );
  }
}

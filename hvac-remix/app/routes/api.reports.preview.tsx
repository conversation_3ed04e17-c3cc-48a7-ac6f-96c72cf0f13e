/**
 * Report Preview API Route
 * 
 * Generates preview data for custom reports using the enhanced GoBackend bridge
 * Supports field selection, filtering, grouping, and aggregations
 */

import type { ActionFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { goBackendBridge } from '~/services/gobackend-bridge.server';

interface ReportField {
  id: string;
  name: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'currency';
  table: string;
  aggregatable?: boolean;
}

interface ReportFilter {
  id: string;
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'between' | 'in';
  value: any;
  label: string;
}

interface ReportConfig {
  name: string;
  description: string;
  selectedFields: ReportField[];
  filters: ReportFilter[];
  grouping: any[];
  sorting: any[];
  aggregations: Record<string, string>;
}

export async function action({ request }: ActionFunctionArgs) {
  try {
    const reportConfig: ReportConfig = await request.json();
    
    // Validate report configuration
    if (!reportConfig.selectedFields || reportConfig.selectedFields.length === 0) {
      return json(
        { error: 'At least one field must be selected' },
        { status: 400 }
      );
    }

    // Build SQL query based on report configuration
    const query = buildSQLQuery(reportConfig);
    
    // Execute query through GoBackend bridge with caching
    const cacheKey = `report_preview:${JSON.stringify(reportConfig)}`;
    const result = await goBackendBridge.executeCustomQuery(
      query,
      { cacheKey, cacheTTL: 300000 } // 5 minutes cache
    );

    if (!result.success) {
      return json(
        { error: result.error?.message || 'Failed to execute query' },
        { status: 500 }
      );
    }

    // Process and format the results
    const processedData = processQueryResults(result.data, reportConfig);

    return json({
      success: true,
      rows: processedData,
      totalRows: processedData.length,
      executionTime: result.metadata?.latency,
      fromCache: result.metadata?.fromCache,
    });

  } catch (error) {
    console.error('Report preview error:', error);
    return json(
      { error: 'Failed to generate report preview' },
      { status: 500 }
    );
  }
}

function buildSQLQuery(config: ReportConfig): string {
  const { selectedFields, filters, grouping, sorting } = config;
  
  // Build SELECT clause
  const selectFields = selectedFields.map(field => {
    if (config.aggregations[field.id]) {
      const aggType = config.aggregations[field.id];
      return `${aggType.toUpperCase()}(${field.name}) as ${field.id}`;
    }
    return `${field.name} as ${field.id}`;
  });

  // Determine main table and joins
  const tables = [...new Set(selectedFields.map(f => f.table))];
  const mainTable = tables[0] || 'customers';
  
  let query = `SELECT ${selectFields.join(', ')} FROM ${mainTable}`;
  
  // Add JOINs for related tables
  if (tables.includes('jobs') && mainTable !== 'jobs') {
    query += ' LEFT JOIN jobs ON customers.id = jobs.customer_id';
  }
  if (tables.includes('services') && !query.includes('services')) {
    query += ' LEFT JOIN services ON jobs.id = services.job_id';
  }
  if (tables.includes('technicians') && !query.includes('technicians')) {
    query += ' LEFT JOIN technicians ON jobs.technician_id = technicians.id';
  }

  // Add WHERE clause for filters
  if (filters.length > 0) {
    const whereConditions = filters.map(filter => buildFilterCondition(filter, selectedFields));
    query += ` WHERE ${whereConditions.join(' AND ')}`;
  }

  // Add GROUP BY clause
  if (grouping.length > 0) {
    const groupFields = grouping.map(g => g.field);
    query += ` GROUP BY ${groupFields.join(', ')}`;
  }

  // Add ORDER BY clause
  if (sorting.length > 0) {
    const orderFields = sorting.map(s => `${s.field} ${s.direction.toUpperCase()}`);
    query += ` ORDER BY ${orderFields.join(', ')}`;
  }

  // Limit for preview
  query += ' LIMIT 100';

  return query;
}

function buildFilterCondition(filter: ReportFilter, fields: ReportField[]): string {
  const field = fields.find(f => f.id === filter.field);
  if (!field) return '1=1';

  const fieldName = field.name;
  const { operator, value } = filter;

  switch (operator) {
    case 'equals':
      return `${fieldName} = '${value}'`;
    case 'contains':
      return `${fieldName} ILIKE '%${value}%'`;
    case 'greater_than':
      return `${fieldName} > '${value}'`;
    case 'less_than':
      return `${fieldName} < '${value}'`;
    case 'between':
      const [start, end] = value.split(',');
      return `${fieldName} BETWEEN '${start}' AND '${end}'`;
    case 'in':
      const values = value.split(',').map((v: string) => `'${v.trim()}'`).join(',');
      return `${fieldName} IN (${values})`;
    default:
      return '1=1';
  }
}

function processQueryResults(data: any[], config: ReportConfig): any[] {
  if (!data || !Array.isArray(data)) return [];

  return data.map(row => {
    const processedRow: any = {};
    
    config.selectedFields.forEach(field => {
      let value = row[field.id];
      
      // Format values based on field type
      switch (field.type) {
        case 'currency':
          processedRow[field.name] = formatCurrency(value);
          break;
        case 'date':
          processedRow[field.name] = formatDate(value);
          break;
        case 'number':
          processedRow[field.name] = formatNumber(value);
          break;
        default:
          processedRow[field.name] = value || '';
      }
    });
    
    return processedRow;
  });
}

function formatCurrency(value: any): string {
  if (value === null || value === undefined) return '$0.00';
  const num = parseFloat(value);
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(isNaN(num) ? 0 : num);
}

function formatDate(value: any): string {
  if (!value) return '';
  try {
    return new Date(value).toLocaleDateString();
  } catch {
    return value;
  }
}

function formatNumber(value: any): string {
  if (value === null || value === undefined) return '0';
  const num = parseFloat(value);
  return isNaN(num) ? '0' : num.toLocaleString();
}

/**
 * Report Export API Route
 * 
 * Handles data export requests for custom reports and analytics
 * Supports PDF, Excel, and CSV formats with comprehensive formatting
 */

import type { ActionFunctionArgs } from '@remix-run/node';
import { dataExportService } from '~/services/data-export.server';

export async function action({ request }: ActionFunctionArgs) {
  try {
    const body = await request.json();
    const { reportConfig, format, exportType = 'custom' } = body;

    if (!format || !['pdf', 'excel', 'csv'].includes(format)) {
      return new Response('Invalid export format', { status: 400 });
    }

    let exportBuffer: Buffer;
    let filename: string;
    let contentType: string;

    switch (exportType) {
      case 'analytics':
        exportBuffer = await dataExportService.exportAnalyticsDashboard(
          {
            from: new Date(body.dateRange.from),
            to: new Date(body.dateRange.to),
          },
          format
        );
        filename = `analytics-report-${new Date().toISOString().split('T')[0]}`;
        break;

      case 'customers':
        exportBuffer = await dataExportService.exportCustomerReport(
          body.filters || {},
          format
        );
        filename = `customer-report-${new Date().toISOString().split('T')[0]}`;
        break;

      case 'custom':
      default:
        // Build export config from report configuration
        const exportConfig = {
          title: reportConfig.name || 'Custom Report',
          description: reportConfig.description || 'Generated custom report',
          data: body.data || [],
          columns: reportConfig.selectedFields.map((field: any) => ({
            key: field.name,
            label: field.label,
            type: field.type,
            width: getColumnWidth(field.type),
          })),
          format,
          metadata: {
            generatedAt: new Date(),
            generatedBy: 'HVAC CRM System',
            totalRows: body.data?.length || 0,
            filters: reportConfig.filters?.map((f: any) => 
              `${f.label}: ${f.operator} ${f.value}`
            ) || [],
          },
        };

        exportBuffer = await dataExportService.exportData(exportConfig);
        filename = reportConfig.name 
          ? reportConfig.name.toLowerCase().replace(/\s+/g, '-')
          : 'custom-report';
        break;
    }

    // Set content type based on format
    switch (format) {
      case 'pdf':
        contentType = 'application/pdf';
        filename += '.pdf';
        break;
      case 'excel':
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename += '.xlsx';
        break;
      case 'csv':
        contentType = 'text/csv';
        filename += '.csv';
        break;
      default:
        contentType = 'application/octet-stream';
    }

    // Return the file
    return new Response(exportBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': exportBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Export error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Failed to export data',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

function getColumnWidth(fieldType: string): number {
  switch (fieldType) {
    case 'currency':
      return 15;
    case 'date':
      return 12;
    case 'number':
      return 10;
    case 'boolean':
      return 8;
    default:
      return 20;
  }
}

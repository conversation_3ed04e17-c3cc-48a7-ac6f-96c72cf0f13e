/**
 * GoBackend-Kratos Connection Pool Implementation
 * 
 * Manages connection pooling for optimal performance with GoBackend-Kratos
 * Implements connection reuse, health monitoring, and automatic cleanup
 */

import { gobackendClient } from '~/lib/gobackend-client';

interface Connection {
  id: string;
  isActive: boolean;
  lastUsed: Date;
  createdAt: Date;
  execute<T>(operation: () => Promise<T>): Promise<T>;
}

interface PoolStats {
  total: number;
  active: number;
  idle: number;
  waiting: number;
}

interface ConnectionPoolConfig {
  maxConnections: number;
  minConnections: number;
  acquireTimeoutMillis: number;
  idleTimeoutMillis: number;
}

/**
 * GoBackend Connection Pool Implementation
 */
export class GoBackendConnectionPool {
  private connections: Map<string, Connection> = new Map();
  private waitingQueue: Array<{
    resolve: (connection: Connection) => void;
    reject: (error: Error) => void;
    timestamp: Date;
  }> = [];
  private config: ConnectionPoolConfig;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(config?: Partial<ConnectionPoolConfig>) {
    this.config = {
      maxConnections: 10,
      minConnections: 2,
      acquireTimeoutMillis: 30000,
      idleTimeoutMillis: 300000, // 5 minutes
      ...config,
    };

    // Initialize minimum connections
    this.initializeMinConnections();
    
    // Start cleanup process
    this.startCleanupProcess();
  }

  /**
   * Acquire a connection from the pool
   */
  async acquire(): Promise<Connection> {
    // Try to get an idle connection first
    const idleConnection = this.getIdleConnection();
    if (idleConnection) {
      idleConnection.isActive = true;
      idleConnection.lastUsed = new Date();
      return idleConnection;
    }

    // Create new connection if under limit
    if (this.connections.size < this.config.maxConnections) {
      const connection = await this.createConnection();
      this.connections.set(connection.id, connection);
      return connection;
    }

    // Wait for available connection
    return this.waitForConnection();
  }

  /**
   * Release a connection back to the pool
   */
  release(connection: Connection): void {
    const poolConnection = this.connections.get(connection.id);
    if (poolConnection) {
      poolConnection.isActive = false;
      poolConnection.lastUsed = new Date();

      // If there are waiting requests, fulfill them
      if (this.waitingQueue.length > 0) {
        const waiter = this.waitingQueue.shift();
        if (waiter) {
          poolConnection.isActive = true;
          waiter.resolve(poolConnection);
        }
      }
    }
  }

  /**
   * Destroy the connection pool
   */
  async destroy(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Reject all waiting requests
    this.waitingQueue.forEach(waiter => {
      waiter.reject(new Error('Connection pool is being destroyed'));
    });
    this.waitingQueue = [];

    // Clear all connections
    this.connections.clear();
  }

  /**
   * Get pool statistics
   */
  getStats(): PoolStats {
    const total = this.connections.size;
    const active = Array.from(this.connections.values()).filter(c => c.isActive).length;
    const idle = total - active;
    const waiting = this.waitingQueue.length;

    return { total, active, idle, waiting };
  }

  /**
   * Initialize minimum connections
   */
  private async initializeMinConnections(): Promise<void> {
    const promises = [];
    for (let i = 0; i < this.config.minConnections; i++) {
      promises.push(this.createConnection().then(conn => {
        this.connections.set(conn.id, conn);
      }));
    }
    await Promise.all(promises);
  }

  /**
   * Create a new connection
   */
  private async createConnection(): Promise<Connection> {
    const id = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const connection: Connection = {
      id,
      isActive: true,
      lastUsed: new Date(),
      createdAt: new Date(),
      async execute<T>(operation: () => Promise<T>): Promise<T> {
        try {
          this.lastUsed = new Date();
          return await operation();
        } catch (error) {
          throw error;
        }
      },
    };

    return connection;
  }

  /**
   * Get an idle connection
   */
  private getIdleConnection(): Connection | null {
    for (const connection of this.connections.values()) {
      if (!connection.isActive) {
        return connection;
      }
    }
    return null;
  }

  /**
   * Wait for an available connection
   */
  private async waitForConnection(): Promise<Connection> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        const index = this.waitingQueue.findIndex(w => w.resolve === resolve);
        if (index !== -1) {
          this.waitingQueue.splice(index, 1);
        }
        reject(new Error('Connection acquire timeout'));
      }, this.config.acquireTimeoutMillis);

      this.waitingQueue.push({
        resolve: (connection) => {
          clearTimeout(timeout);
          resolve(connection);
        },
        reject: (error) => {
          clearTimeout(timeout);
          reject(error);
        },
        timestamp: new Date(),
      });
    });
  }

  /**
   * Start cleanup process for idle connections
   */
  private startCleanupProcess(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupIdleConnections();
    }, 60000); // Run every minute
  }

  /**
   * Clean up idle connections that have exceeded the idle timeout
   */
  private cleanupIdleConnections(): void {
    const now = new Date();
    const connectionsToRemove: string[] = [];

    for (const [id, connection] of this.connections.entries()) {
      if (!connection.isActive) {
        const idleTime = now.getTime() - connection.lastUsed.getTime();
        if (idleTime > this.config.idleTimeoutMillis) {
          connectionsToRemove.push(id);
        }
      }
    }

    // Remove idle connections but maintain minimum
    const currentCount = this.connections.size;
    const maxToRemove = Math.max(0, currentCount - this.config.minConnections);
    const toRemove = connectionsToRemove.slice(0, maxToRemove);

    toRemove.forEach(id => {
      this.connections.delete(id);
    });

    if (toRemove.length > 0) {
      console.log(`Cleaned up ${toRemove.length} idle connections`);
    }
  }
}

export default GoBackendConnectionPool;

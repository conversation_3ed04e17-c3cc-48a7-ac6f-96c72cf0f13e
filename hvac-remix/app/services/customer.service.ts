/**
 * Customer Service - Frontend Bridge to GoBackend-Kratos
 * 
 * This service provides a frontend interface to customer operations
 * that are handled by GoBackend-Kratos backend.
 */

import { goBackendBridge } from './gobackend-bridge.server';

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Get all customers with optional filtering
 */
export async function getCustomers(filters?: {
  search?: string;
  status?: 'active' | 'inactive';
  region?: string;
  limit?: number;
  offset?: number;
}) {
  const response = await goBackendBridge.getCustomers(filters);
  if (response.success) {
    return response.data || [];
  }
  throw new Error(response.error?.message || 'Failed to fetch customers');
}

/**
 * Get a customer by ID
 */
export async function getCustomerById(id: string): Promise<Customer> {
  const response = await goBackendBridge.getCustomer(id);
  if (response.success && response.data) {
    return response.data;
  }
  throw new Error(response.error?.message || 'Customer not found');
}

/**
 * Create a new customer
 */
export async function createCustomer(data: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<Customer> {
  const response = await goBackendBridge.createCustomer(data);
  if (response.success && response.data) {
    return response.data;
  }
  throw new Error(response.error?.message || 'Failed to create customer');
}

/**
 * Update an existing customer
 */
export async function updateCustomer(id: string, data: Partial<Customer>): Promise<Customer> {
  const response = await goBackendBridge.updateCustomer(id, data);
  if (response.success && response.data) {
    return response.data;
  }
  throw new Error(response.error?.message || 'Failed to update customer');
}

/**
 * Delete a customer
 */
export async function deleteCustomer(id: string): Promise<void> {
  // TODO: Implement delete customer in GoBackend-Kratos
  throw new Error('Delete customer not yet implemented');
}

export type { Customer };

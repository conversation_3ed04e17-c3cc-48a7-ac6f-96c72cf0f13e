/**
 * Data Export Service
 * 
 * Comprehensive data export system supporting PDF, Excel, and CSV formats
 * Integrates with GoBackend-Kratos for data retrieval and caching
 */

import * as XLSX from 'xlsx';
import PDFDocument from 'pdfkit';
import { goBackendBridge } from './gobackend-bridge.server';

interface ExportConfig {
  title: string;
  description?: string;
  data: any[];
  columns: Array<{
    key: string;
    label: string;
    type?: 'string' | 'number' | 'date' | 'currency';
    width?: number;
  }>;
  format: 'pdf' | 'excel' | 'csv';
  metadata?: {
    generatedBy?: string;
    generatedAt?: Date;
    filters?: string[];
    totalRows?: number;
  };
}

export class DataExportService {
  private static instance: DataExportService;

  static getInstance(): DataExportService {
    if (!DataExportService.instance) {
      DataExportService.instance = new DataExportService();
    }
    return DataExportService.instance;
  }

  /**
   * Export data in the specified format
   */
  async exportData(config: ExportConfig): Promise<Buffer> {
    switch (config.format) {
      case 'pdf':
        return this.exportToPDF(config);
      case 'excel':
        return this.exportToExcel(config);
      case 'csv':
        return this.exportToCSV(config);
      default:
        throw new Error(`Unsupported export format: ${config.format}`);
    }
  }

  /**
   * Export data to PDF format
   */
  private async exportToPDF(config: ExportConfig): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50 });
        const chunks: Buffer[] = [];

        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Header
        doc.fontSize(20).text(config.title, { align: 'center' });
        doc.moveDown();

        if (config.description) {
          doc.fontSize(12).text(config.description, { align: 'center' });
          doc.moveDown();
        }

        // Metadata
        if (config.metadata) {
          doc.fontSize(10);
          if (config.metadata.generatedAt) {
            doc.text(`Generated: ${config.metadata.generatedAt.toLocaleString()}`);
          }
          if (config.metadata.generatedBy) {
            doc.text(`Generated by: ${config.metadata.generatedBy}`);
          }
          if (config.metadata.totalRows) {
            doc.text(`Total rows: ${config.metadata.totalRows}`);
          }
          doc.moveDown();
        }

        // Table setup
        const tableTop = doc.y;
        const itemHeight = 20;
        const pageWidth = doc.page.width - 100;
        const columnWidth = pageWidth / config.columns.length;

        // Table headers
        doc.fontSize(10).fillColor('black');
        config.columns.forEach((column, i) => {
          doc.text(
            column.label,
            50 + i * columnWidth,
            tableTop,
            { width: columnWidth, align: 'left' }
          );
        });

        // Header underline
        doc.moveTo(50, tableTop + 15)
           .lineTo(50 + pageWidth, tableTop + 15)
           .stroke();

        // Table rows
        let currentY = tableTop + itemHeight;
        config.data.forEach((row, rowIndex) => {
          // Check if we need a new page
          if (currentY > doc.page.height - 100) {
            doc.addPage();
            currentY = 50;
          }

          config.columns.forEach((column, colIndex) => {
            let value = row[column.key] || '';
            
            // Format value based on type
            if (column.type === 'currency' && typeof value === 'number') {
              value = this.formatCurrency(value);
            } else if (column.type === 'date' && value) {
              value = new Date(value).toLocaleDateString();
            } else if (column.type === 'number' && typeof value === 'number') {
              value = value.toLocaleString();
            }

            doc.text(
              String(value),
              50 + colIndex * columnWidth,
              currentY,
              { width: columnWidth, align: 'left' }
            );
          });

          currentY += itemHeight;
        });

        // Footer
        const pageCount = doc.bufferedPageRange().count;
        for (let i = 0; i < pageCount; i++) {
          doc.switchToPage(i);
          doc.fontSize(8)
             .text(
               `Page ${i + 1} of ${pageCount}`,
               50,
               doc.page.height - 50,
               { align: 'center' }
             );
        }

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Export data to Excel format
   */
  private async exportToExcel(config: ExportConfig): Promise<Buffer> {
    const workbook = XLSX.utils.book_new();

    // Create main data worksheet
    const worksheetData = [
      // Headers
      config.columns.map(col => col.label),
      // Data rows
      ...config.data.map(row => 
        config.columns.map(col => {
          let value = row[col.key];
          
          // Format value based on type
          if (col.type === 'currency' && typeof value === 'number') {
            return value; // Keep as number for Excel formatting
          } else if (col.type === 'date' && value) {
            return new Date(value);
          } else if (col.type === 'number' && typeof value === 'number') {
            return value;
          }
          
          return value || '';
        })
      )
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // Set column widths
    const columnWidths = config.columns.map(col => ({
      wch: col.width || 15
    }));
    worksheet['!cols'] = columnWidths;

    // Format currency columns
    config.columns.forEach((col, index) => {
      if (col.type === 'currency') {
        const columnLetter = XLSX.utils.encode_col(index);
        for (let row = 1; row <= config.data.length; row++) {
          const cellAddress = `${columnLetter}${row + 1}`;
          if (worksheet[cellAddress]) {
            worksheet[cellAddress].z = '"$"#,##0.00';
          }
        }
      }
    });

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

    // Create metadata worksheet if available
    if (config.metadata) {
      const metadataData = [
        ['Report Title', config.title],
        ['Description', config.description || ''],
        ['Generated At', config.metadata.generatedAt?.toLocaleString() || ''],
        ['Generated By', config.metadata.generatedBy || ''],
        ['Total Rows', config.metadata.totalRows || config.data.length],
        ['Filters Applied', config.metadata.filters?.join(', ') || 'None'],
      ];

      const metadataWorksheet = XLSX.utils.aoa_to_sheet(metadataData);
      XLSX.utils.book_append_sheet(workbook, metadataWorksheet, 'Metadata');
    }

    // Convert to buffer
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx' 
    });

    return excelBuffer;
  }

  /**
   * Export data to CSV format
   */
  private async exportToCSV(config: ExportConfig): Promise<Buffer> {
    const csvRows: string[] = [];

    // Add metadata as comments if available
    if (config.metadata) {
      csvRows.push(`# ${config.title}`);
      if (config.description) {
        csvRows.push(`# ${config.description}`);
      }
      if (config.metadata.generatedAt) {
        csvRows.push(`# Generated: ${config.metadata.generatedAt.toLocaleString()}`);
      }
      if (config.metadata.totalRows) {
        csvRows.push(`# Total rows: ${config.metadata.totalRows}`);
      }
      csvRows.push(''); // Empty line
    }

    // Headers
    const headers = config.columns.map(col => this.escapeCSVField(col.label));
    csvRows.push(headers.join(','));

    // Data rows
    config.data.forEach(row => {
      const csvRow = config.columns.map(col => {
        let value = row[col.key] || '';
        
        // Format value based on type
        if (col.type === 'currency' && typeof value === 'number') {
          value = this.formatCurrency(value);
        } else if (col.type === 'date' && value) {
          value = new Date(value).toLocaleDateString();
        } else if (col.type === 'number' && typeof value === 'number') {
          value = value.toLocaleString();
        }

        return this.escapeCSVField(String(value));
      });
      
      csvRows.push(csvRow.join(','));
    });

    return Buffer.from(csvRows.join('\n'), 'utf-8');
  }

  /**
   * Export analytics dashboard data
   */
  async exportAnalyticsDashboard(
    dateRange: { from: Date; to: Date },
    format: 'pdf' | 'excel' | 'csv'
  ): Promise<Buffer> {
    // Fetch analytics data through GoBackend bridge
    const analyticsResult = await goBackendBridge.getAnalyticsData(dateRange);
    
    if (!analyticsResult.success) {
      throw new Error('Failed to fetch analytics data');
    }

    const config: ExportConfig = {
      title: 'HVAC Analytics Dashboard Report',
      description: `Analytics report for ${dateRange.from.toLocaleDateString()} to ${dateRange.to.toLocaleDateString()}`,
      data: analyticsResult.data.summary || [],
      columns: [
        { key: 'metric', label: 'Metric', type: 'string', width: 20 },
        { key: 'value', label: 'Value', type: 'number', width: 15 },
        { key: 'change', label: 'Change %', type: 'number', width: 15 },
        { key: 'period', label: 'Period', type: 'string', width: 15 },
      ],
      format,
      metadata: {
        generatedAt: new Date(),
        generatedBy: 'HVAC CRM System',
        totalRows: analyticsResult.data.summary?.length || 0,
      },
    };

    return this.exportData(config);
  }

  /**
   * Export customer report
   */
  async exportCustomerReport(
    filters: any,
    format: 'pdf' | 'excel' | 'csv'
  ): Promise<Buffer> {
    const customersResult = await goBackendBridge.getCustomers(filters);
    
    if (!customersResult.success) {
      throw new Error('Failed to fetch customer data');
    }

    const config: ExportConfig = {
      title: 'Customer Report',
      description: 'Comprehensive customer data export',
      data: customersResult.data || [],
      columns: [
        { key: 'name', label: 'Customer Name', type: 'string', width: 25 },
        { key: 'email', label: 'Email', type: 'string', width: 30 },
        { key: 'phone', label: 'Phone', type: 'string', width: 15 },
        { key: 'address', label: 'Address', type: 'string', width: 35 },
        { key: 'created_at', label: 'Customer Since', type: 'date', width: 15 },
        { key: 'total_jobs', label: 'Total Jobs', type: 'number', width: 12 },
        { key: 'total_revenue', label: 'Total Revenue', type: 'currency', width: 15 },
      ],
      format,
      metadata: {
        generatedAt: new Date(),
        generatedBy: 'HVAC CRM System',
        totalRows: customersResult.data?.length || 0,
        filters: Object.entries(filters).map(([key, value]) => `${key}: ${value}`),
      },
    };

    return this.exportData(config);
  }

  /**
   * Helper methods
   */
  private escapeCSVField(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  private formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  }
}

export const dataExportService = DataExportService.getInstance();

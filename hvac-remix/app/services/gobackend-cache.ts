/**
 * GoBackend-Kratos Cache Implementation
 * 
 * High-performance caching layer for GoBackend API responses
 * Supports TTL, compression, and intelligent cache invalidation
 */

import { createHash } from 'crypto';
import { gzip, gunzip } from 'zlib';
import { promisify } from 'util';

const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

interface CacheEntry<T> {
  data: T;
  timestamp: Date;
  ttl: number;
  compressed?: boolean;
  accessCount: number;
  lastAccessed: Date;
}

interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: number;
  memoryUsage: number;
}

interface CacheConfig {
  defaultTTL: number;
  maxSize: number;
  enableCompression: boolean;
  compressionThreshold: number; // bytes
}

/**
 * GoBackend Cache Implementation with LRU eviction and compression
 */
export class GoBackendCache {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private stats: CacheStats = {
    size: 0,
    hits: 0,
    misses: 0,
    hitRate: 0,
    memoryUsage: 0,
  };
  private config: CacheConfig;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(config?: Partial<CacheConfig>) {
    this.config = {
      defaultTTL: 300000, // 5 minutes
      maxSize: 1000,
      enableCompression: true,
      compressionThreshold: 1024, // 1KB
      ...config,
    };

    // Start cleanup process
    this.startCleanupProcess();
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    const cacheKey = this.generateKey(key);
    const entry = this.cache.get(cacheKey);

    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Check if entry has expired
    const now = new Date();
    if (now.getTime() - entry.timestamp.getTime() > entry.ttl) {
      this.cache.delete(cacheKey);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = now;
    this.stats.hits++;
    this.updateHitRate();

    // Decompress if needed
    if (entry.compressed && this.config.enableCompression) {
      try {
        const decompressed = await gunzipAsync(entry.data as Buffer);
        return JSON.parse(decompressed.toString()) as T;
      } catch (error) {
        console.error('Cache decompression error:', error);
        this.cache.delete(cacheKey);
        return null;
      }
    }

    return entry.data as T;
  }

  /**
   * Set value in cache
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const cacheKey = this.generateKey(key);
    const now = new Date();
    const entryTTL = ttl || this.config.defaultTTL;

    let dataToStore: any = value;
    let compressed = false;

    // Compress large data if enabled
    if (this.config.enableCompression) {
      const serialized = JSON.stringify(value);
      const sizeInBytes = Buffer.byteLength(serialized, 'utf8');

      if (sizeInBytes > this.config.compressionThreshold) {
        try {
          dataToStore = await gzipAsync(serialized);
          compressed = true;
        } catch (error) {
          console.error('Cache compression error:', error);
          // Fall back to uncompressed storage
          dataToStore = value;
        }
      }
    }

    const entry: CacheEntry<T> = {
      data: dataToStore,
      timestamp: now,
      ttl: entryTTL,
      compressed,
      accessCount: 0,
      lastAccessed: now,
    };

    // Check if we need to evict entries
    if (this.cache.size >= this.config.maxSize) {
      this.evictLRU();
    }

    this.cache.set(cacheKey, entry);
    this.stats.size = this.cache.size;
    this.updateMemoryUsage();
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<void> {
    const cacheKey = this.generateKey(key);
    const deleted = this.cache.delete(cacheKey);
    
    if (deleted) {
      this.stats.size = this.cache.size;
      this.updateMemoryUsage();
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    this.cache.clear();
    this.stats.size = 0;
    this.stats.hits = 0;
    this.stats.misses = 0;
    this.stats.hitRate = 0;
    this.stats.memoryUsage = 0;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Invalidate cache entries by pattern
   */
  async invalidatePattern(pattern: string): Promise<number> {
    const regex = new RegExp(pattern);
    const keysToDelete: string[] = [];

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    this.stats.size = this.cache.size;
    this.updateMemoryUsage();

    return keysToDelete.length;
  }

  /**
   * Get cache entry info for debugging
   */
  getEntryInfo(key: string): any {
    const cacheKey = this.generateKey(key);
    const entry = this.cache.get(cacheKey);
    
    if (!entry) {
      return null;
    }

    return {
      key: cacheKey,
      timestamp: entry.timestamp,
      ttl: entry.ttl,
      compressed: entry.compressed,
      accessCount: entry.accessCount,
      lastAccessed: entry.lastAccessed,
      isExpired: new Date().getTime() - entry.timestamp.getTime() > entry.ttl,
    };
  }

  /**
   * Destroy cache and cleanup
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.cache.clear();
  }

  /**
   * Generate cache key with hash
   */
  private generateKey(key: string): string {
    return createHash('md5').update(key).digest('hex');
  }

  /**
   * Update hit rate calculation
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }

  /**
   * Estimate memory usage
   */
  private updateMemoryUsage(): void {
    let totalSize = 0;
    
    for (const entry of this.cache.values()) {
      if (entry.compressed) {
        totalSize += (entry.data as Buffer).length;
      } else {
        totalSize += Buffer.byteLength(JSON.stringify(entry.data), 'utf8');
      }
    }
    
    this.stats.memoryUsage = totalSize;
  }

  /**
   * Evict least recently used entries
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = new Date();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Start cleanup process for expired entries
   */
  private startCleanupProcess(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries();
    }, 60000); // Run every minute
  }

  /**
   * Clean up expired entries
   */
  private cleanupExpiredEntries(): void {
    const now = new Date();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now.getTime() - entry.timestamp.getTime() > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      this.stats.size = this.cache.size;
      this.updateMemoryUsage();
      console.log(`Cleaned up ${keysToDelete.length} expired cache entries`);
    }
  }
}

export default GoBackendCache;

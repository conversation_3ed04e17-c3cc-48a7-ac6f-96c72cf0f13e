/**
 * Service Order Service - Frontend Bridge to GoBackend-Kratos
 * 
 * This service provides a frontend interface to service order operations
 * that are handled by GoBackend-Kratos backend.
 */

import { goBackendBridge } from './gobackend-bridge.server';

export interface ServiceOrder {
  id: string;
  customerId: string;
  deviceId?: string;
  title: string;
  description: string;
  status: 'pending' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  technicianId?: string;
  scheduledAt?: Date;
  completedAt?: Date;
  estimatedDuration?: number;
  actualDuration?: number;
  cost?: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Get all service orders with optional filtering
 */
export async function getServiceOrders(filters?: {
  customerId?: string;
  status?: ServiceOrder['status'];
  priority?: ServiceOrder['priority'];
  technicianId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
  offset?: number;
}): Promise<ServiceOrder[]> {
  const response = await goBackendBridge.getJobs(filters);
  if (response.success) {
    // Transform Job data to ServiceOrder format
    return (response.data || []).map(job => ({
      id: job.id.toString(),
      customerId: job.customer_id.toString(),
      title: job.title,
      description: job.description,
      status: job.status as ServiceOrder['status'],
      priority: job.priority as ServiceOrder['priority'],
      scheduledAt: job.scheduled_at ? new Date(job.scheduled_at) : undefined,
      createdAt: new Date(job.created_at),
      updatedAt: new Date(job.updated_at),
    }));
  }
  throw new Error(response.error?.message || 'Failed to fetch service orders');
}

/**
 * Get a service order by ID
 */
export async function getServiceOrderById(id: string): Promise<ServiceOrder> {
  const response = await goBackendBridge.getJob(id);
  if (response.success && response.data) {
    const job = response.data;
    return {
      id: job.id.toString(),
      customerId: job.customer_id.toString(),
      title: job.title,
      description: job.description,
      status: job.status as ServiceOrder['status'],
      priority: job.priority as ServiceOrder['priority'],
      scheduledAt: job.scheduled_at ? new Date(job.scheduled_at) : undefined,
      createdAt: new Date(job.created_at),
      updatedAt: new Date(job.updated_at),
    };
  }
  throw new Error(response.error?.message || 'Service order not found');
}

/**
 * Create a new service order
 */
export async function createServiceOrder(data: Omit<ServiceOrder, 'id' | 'createdAt' | 'updatedAt'>): Promise<ServiceOrder> {
  const jobData = {
    customer_id: parseInt(data.customerId),
    title: data.title,
    description: data.description,
    priority: data.priority,
    scheduled_at: data.scheduledAt,
  };

  const response = await goBackendBridge.createJob(jobData);
  if (response.success && response.data) {
    const job = response.data;
    return {
      id: job.id.toString(),
      customerId: job.customer_id.toString(),
      title: job.title,
      description: job.description,
      status: job.status as ServiceOrder['status'],
      priority: job.priority as ServiceOrder['priority'],
      scheduledAt: job.scheduled_at ? new Date(job.scheduled_at) : undefined,
      createdAt: new Date(job.created_at),
      updatedAt: new Date(job.updated_at),
    };
  }
  throw new Error(response.error?.message || 'Failed to create service order');
}

/**
 * Update an existing service order
 */
export async function updateServiceOrder(id: string, data: Partial<ServiceOrder>): Promise<ServiceOrder> {
  // TODO: Implement updateJob in GoBackend-Kratos
  throw new Error('Update service order not yet implemented');
}

/**
 * Delete a service order
 */
export async function deleteServiceOrder(id: string): Promise<void> {
  // TODO: Implement deleteJob in GoBackend-Kratos
  throw new Error('Delete service order not yet implemented');
}

export type { ServiceOrder };

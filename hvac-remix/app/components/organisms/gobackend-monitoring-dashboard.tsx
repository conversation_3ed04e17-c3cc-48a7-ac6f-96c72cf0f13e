/**
 * GoBackend-Kratos Monitoring Dashboard
 * 
 * Real-time monitoring dashboard for GoBackend integration performance
 * Shows connection pool stats, cache performance, and system health
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Progress } from '~/components/ui/progress';
import { 
  Activity, 
  Database, 
  Zap, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  TrendingUp,
  Clock,
  Server
} from 'lucide-react';

interface Metrics {
  requestCount: number;
  errorCount: number;
  totalLatency: number;
  averageLatency: number;
  errorRate: number;
  lastError?: Date;
  healthCheckFailures: number;
}

interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: number;
  memoryUsage: number;
}

interface PoolStats {
  total: number;
  active: number;
  idle: number;
  waiting: number;
}

interface SystemHealth {
  status: 'healthy' | 'unhealthy' | 'unknown';
  services: Record<string, boolean>;
  latency: number;
}

interface MonitoringData {
  metrics: Metrics;
  cacheStats: CacheStats;
  poolStats: PoolStats;
  systemHealth: SystemHealth;
  lastUpdated: Date;
}

export function GoBackendMonitoringDashboard() {
  const [data, setData] = useState<MonitoringData | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchMonitoringData = async () => {
    try {
      const response = await fetch('/api/gobackend/monitoring');
      if (response.ok) {
        const monitoringData = await response.json();
        setData(monitoringData);
      }
    } catch (error) {
      console.error('Failed to fetch monitoring data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMonitoringData();
    
    if (autoRefresh) {
      const interval = setInterval(fetchMonitoringData, 30000); // 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'unhealthy': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'unhealthy': return <XCircle className="h-5 w-5 text-red-600" />;
      default: return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading monitoring data...</span>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center h-64">
        <AlertTriangle className="h-8 w-8 text-yellow-600" />
        <span className="ml-2">Failed to load monitoring data</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">GoBackend-Kratos Monitoring</h2>
          <p className="text-muted-foreground">
            Real-time performance and health monitoring
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Activity className="h-4 w-4 mr-2" />
            Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button variant="outline" size="sm" onClick={fetchMonitoringData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            {getHealthStatusIcon(data.systemHealth.status)}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getHealthStatusColor(data.systemHealth.status)}`}>
              {data.systemHealth.status.toUpperCase()}
            </div>
            <p className="text-xs text-muted-foreground">
              Latency: {data.systemHealth.latency}ms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Request Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.metrics.requestCount}</div>
            <p className="text-xs text-muted-foreground">
              Error Rate: {formatPercentage(data.metrics.errorRate)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Latency</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.metrics.averageLatency.toFixed(0)}ms</div>
            <p className="text-xs text-muted-foreground">
              Total: {data.metrics.totalLatency}ms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(data.cacheStats.hitRate)}</div>
            <p className="text-xs text-muted-foreground">
              {data.cacheStats.hits} hits / {data.cacheStats.misses} misses
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Connection Pool Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Server className="h-5 w-5 mr-2" />
              Connection Pool
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Total Connections</span>
                <Badge variant="secondary">{data.poolStats.total}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Active</span>
                <Badge variant="default">{data.poolStats.active}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Idle</span>
                <Badge variant="outline">{data.poolStats.idle}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Waiting</span>
                <Badge variant={data.poolStats.waiting > 0 ? "destructive" : "secondary"}>
                  {data.poolStats.waiting}
                </Badge>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Pool Utilization</span>
                <span>{formatPercentage(data.poolStats.active / data.poolStats.total)}</span>
              </div>
              <Progress 
                value={(data.poolStats.active / data.poolStats.total) * 100} 
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>

        {/* Cache Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Cache Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Cache Size</span>
                <Badge variant="secondary">{data.cacheStats.size} entries</Badge>
              </div>
              <div className="flex justify-between">
                <span>Memory Usage</span>
                <Badge variant="outline">{formatBytes(data.cacheStats.memoryUsage)}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Hit Rate</span>
                <Badge variant={data.cacheStats.hitRate > 0.8 ? "default" : "destructive"}>
                  {formatPercentage(data.cacheStats.hitRate)}
                </Badge>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Cache Efficiency</span>
                <span>{formatPercentage(data.cacheStats.hitRate)}</span>
              </div>
              <Progress 
                value={data.cacheStats.hitRate * 100} 
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Services Status */}
      <Card>
        <CardHeader>
          <CardTitle>Service Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(data.systemHealth.services).map(([service, status]) => (
              <div key={service} className="flex items-center space-x-2">
                {status ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <span className="text-sm">{service}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Last Updated */}
      <div className="text-center text-sm text-muted-foreground">
        Last updated: {data.lastUpdated.toLocaleString()}
      </div>
    </div>
  );
}

export default GoBackendMonitoringDashboard;

// Import Sentry only if it's available
let Sentry: any;
let ProfilingIntegration: any;
try {
  Sentry = require('@sentry/node');
  ProfilingIntegration = require('@sentry/profiling-node').ProfilingIntegration;
} catch (e) {
  // Sentry is not installed, create a mock
  Sentry = {
    init: () => {},
    captureException: () => {},
    captureMessage: () => {},
    startTransaction: () => ({ setMeasurement: () => {}, finish: () => {} }),
    SeverityLevel: { info: 'info' }
  };
  ProfilingIntegration = class MockProfilingIntegration {};
}

// Redis now handled by GoBackend-Kratos
// import { getRedisClient } from './redis.server';

// Define metric types
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  SUMMARY = 'summary'
}

// Define alert levels
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

/**
 * Initialize Sentry for server-side error monitoring
 */
export function initializeServerMonitoring() {
  if (process.env.NODE_ENV === 'production' && process.env.SENTRY_DSN && Sentry.init) {
    try {
      Sentry.init({
        dsn: process.env.SENTRY_DSN,
        environment: process.env.NODE_ENV,
        integrations: [
          new ProfilingIntegration(),
        ],
        // Performance monitoring
        tracesSampleRate: 0.2,
        // Set sampling rate for profiling - this is relative to tracesSampleRate
        profilesSampleRate: 0.1,
      });
    } catch (e) {
      console.error('Failed to initialize Sentry:', e);
    }
  }
}

/**
 * Capture an exception in Sentry
 */
export function captureException(error: Error, context?: Record<string, any>) {
  if (process.env.NODE_ENV === 'production' && process.env.SENTRY_DSN) {
    Sentry.captureException(error, {
      extra: context,
    });
  } else {
    console.error('Error:', error, context);
  }
}

/**
 * Capture a message in Sentry
 */
export function captureMessage(message: string, level: Sentry.SeverityLevel = 'info') {
  if (process.env.NODE_ENV === 'production' && process.env.SENTRY_DSN) {
    Sentry.captureMessage(message, level);
  } else {
    console.log(`[${level}] ${message}`);
  }
}/**
 * Create a transaction for performance monitoring
 */
export function startTransaction(name: string, op: string) {
  if (process.env.NODE_ENV === 'production' && process.env.SENTRY_DSN) {
    return Sentry.startTransaction({
      name,
      op,
    });
  }
  return null;
}

/**
 * Record a custom metric
 * @param name Metric name
 * @param value Metric value
 * @param type Metric type (counter, gauge, histogram, summary)
 * @param tags Additional tags for the metric
 */
export async function recordMetric(
  name: string,
  value: number,
  type: MetricType = MetricType.COUNTER,
  tags: Record<string, string> = {}
) {
  // TODO: Send metrics to GoBackend-Kratos
  console.log(`Metric: ${name} = ${value} (${type})`, tags);
}

/**
 * Get the current value of a metric
 * @param name Metric name
 * @param tags Metric tags
 */
export async function getMetricValue(
  name: string,
  tags: Record<string, string> = {}
): Promise<number | null> {
  // TODO: Get metrics from GoBackend-Kratos
  return null;
}

/**
 * Trigger an alert based on a condition
 * @param name Alert name
 * @param message Alert message
 * @param level Alert level
 * @param tags Additional tags for the alert
 */
export async function triggerAlert(
  name: string,
  message: string,
  level: AlertLevel = AlertLevel.WARNING,
  tags: Record<string, string> = {}
) {
  // Send to Sentry
  captureMessage(`[ALERT] ${name}: ${message}`, level as any);

  // For critical alerts, also send to Sentry as an exception
  if (level === AlertLevel.CRITICAL) {
    captureException(new Error(`Critical Alert: ${name}`), {
      message,
      tags
    });
  }

  // TODO: Send alerts to GoBackend-Kratos
  console.log(`Alert [${level}]: ${name} - ${message}`, tags);
}

/**
 * Check if a metric exceeds a threshold and trigger an alert if it does
 * @param metricName Metric name
 * @param threshold Threshold value
 * @param comparison Comparison function
 * @param alertName Alert name
 * @param alertMessage Alert message
 * @param alertLevel Alert level
 * @param tags Additional tags
 */
export async function checkMetricThreshold(
  metricName: string,
  threshold: number,
  comparison: (value: number, threshold: number) => boolean,
  alertName: string,
  alertMessage: string,
  alertLevel: AlertLevel = AlertLevel.WARNING,
  tags: Record<string, string> = {}
) {
  try {
    const value = await getMetricValue(metricName, tags);

    if (value !== null && comparison(value, threshold)) {
      await triggerAlert(
        alertName,
        `${alertMessage} (${value} ${comparison.name} ${threshold})`,
        alertLevel,
        {
          ...tags,
          metricName,
          threshold: threshold.toString(),
          currentValue: value.toString()
        }
      );

      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking metric threshold:', error);
    return false;
  }
}

// Common comparison functions
export const Comparisons = {
  greaterThan: (value: number, threshold: number) => value > threshold,
  greaterThanOrEqual: (value: number, threshold: number) => value >= threshold,
  lessThan: (value: number, threshold: number) => value < threshold,
  lessThanOrEqual: (value: number, threshold: number) => value <= threshold,
  equals: (value: number, threshold: number) => value === threshold,
  notEquals: (value: number, threshold: number) => value !== threshold
};
# HVAC-Remix CRM Development Docker Compose
# Development environment with hot reloading and debugging capabilities

version: '3.8'

services:
  # HVAC-Remix Development Application
  hvac-remix-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: hvac-remix-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
      - "3001:3001"  # Remix dev server
    environment:
      - NODE_ENV=development
      - PORT=3000
      - HOST=0.0.0.0
      - DATABASE_URL=*************************************************/hvacdb
      - REDIS_URL=redis://redis-dev:6379
      - QDRANT_URL=http://qdrant-dev:6333
      - GOBACKEND_URL=http://gobackend-dev:8080
      - DATABASE_HOST=postgres-dev
      - DATABASE_PORT=5432
      - REDIS_HOST=redis-dev
      - REDIS_PORT=6379
      - GOBACKEND_HOST=gobackend-dev
      - GOBACKEND_PORT=8080
      - SESSION_SECRET=dev-session-secret-key
      - ENCRYPTION_KEY=dev-encryption-key-32-chars
      - JWT_SECRET=dev-jwt-secret-key
      - LOG_LEVEL=debug
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - .:/app
      - /app/node_modules
      - dev-logs:/app/logs
      - dev-uploads:/app/uploads
    depends_on:
      postgres-dev:
        condition: service_healthy
      redis-dev:
        condition: service_healthy
    networks:
      - hvac-dev-network
    command: npm run dev

  # PostgreSQL Development Database
  postgres-dev:
    image: postgres:15-alpine
    container_name: hvac-postgres-dev
    restart: unless-stopped
    environment:
      - POSTGRES_DB=hvacdb
      - POSTGRES_USER=hvacdb
      - POSTGRES_PASSWORD=devpassword
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    networks:
      - hvac-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvacdb -d hvacdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Development Cache
  redis-dev:
    image: redis:7-alpine
    container_name: hvac-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis-dev-data:/data
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    networks:
      - hvac-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Qdrant Development Vector Database
  qdrant-dev:
    image: qdrant/qdrant:latest
    container_name: hvac-qdrant-dev
    restart: unless-stopped
    ports:
      - "6335:6333"  # Different port to avoid conflicts
      - "6336:6334"
    volumes:
      - qdrant-dev-data:/qdrant/storage
    networks:
      - hvac-dev-network

  # GoBackend-Kratos Development
  gobackend-dev:
    image: gobackend-kratos:latest
    container_name: hvac-gobackend-dev
    restart: unless-stopped
    ports:
      - "8081:8080"  # Different port to avoid conflicts
      - "8087:8086"
    environment:
      - GO_ENV=development
      - DATABASE_URL=*************************************************/hvacdb
      - REDIS_URL=redis://redis-dev:6379
      - LM_STUDIO_URL=http://host.docker.internal:1234
      - LOG_LEVEL=debug
    volumes:
      - gobackend-dev-logs:/app/logs
    depends_on:
      postgres-dev:
        condition: service_healthy
      redis-dev:
        condition: service_healthy
    networks:
      - hvac-dev-network

  # Development Database Admin (pgAdmin)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: hvac-pgadmin
    restart: unless-stopped
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=devpassword
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    ports:
      - "5050:80"
    depends_on:
      - postgres-dev
    networks:
      - hvac-dev-network

  # Redis Commander for Redis Management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: hvac-redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
    ports:
      - "8082:8081"
    depends_on:
      - redis-dev
    networks:
      - hvac-dev-network

  # Mailhog for Email Testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: hvac-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - hvac-dev-network

volumes:
  postgres-dev-data:
    driver: local
  redis-dev-data:
    driver: local
  qdrant-dev-data:
    driver: local
  dev-logs:
    driver: local
  dev-uploads:
    driver: local
  gobackend-dev-logs:
    driver: local
  pgadmin-data:
    driver: local

networks:
  hvac-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

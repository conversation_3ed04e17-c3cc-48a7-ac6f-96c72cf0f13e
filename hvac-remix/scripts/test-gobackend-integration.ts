#!/usr/bin/env tsx

/**
 * GoBackend-Kratos Integration Test Script
 * 
 * Tests the enhanced GoBackend integration with connection pooling,
 * caching, and monitoring features
 */

import { goBackendBridge } from '../app/services/gobackend-bridge.server';

interface TestResult {
  name: string;
  success: boolean;
  duration: number;
  error?: string;
  details?: any;
}

class GoBackendIntegrationTester {
  private results: TestResult[] = [];

  async runTest(name: string, testFn: () => Promise<any>): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🧪 Running test: ${name}`);
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      const testResult: TestResult = {
        name,
        success: true,
        duration,
        details: result,
      };
      
      console.log(`✅ ${name} - ${duration}ms`);
      this.results.push(testResult);
      return testResult;
    } catch (error) {
      const duration = Date.now() - startTime;
      const testResult: TestResult = {
        name,
        success: false,
        duration,
        error: error instanceof Error ? error.message : String(error),
      };
      
      console.log(`❌ ${name} - ${duration}ms - ${testResult.error}`);
      this.results.push(testResult);
      return testResult;
    }
  }

  async runAllTests() {
    console.log('🚀 Starting GoBackend-Kratos Integration Tests\n');

    // Test 1: Basic Health Check
    await this.runTest('Health Check', async () => {
      const health = await goBackendBridge.checkHealth();
      if (health.status !== 'healthy') {
        throw new Error(`System is ${health.status}`);
      }
      return health;
    });

    // Test 2: Connection Pool Stats
    await this.runTest('Connection Pool Stats', async () => {
      const stats = goBackendBridge.getConnectionPoolStats();
      if (stats.total === 0) {
        throw new Error('No connections in pool');
      }
      return stats;
    });

    // Test 3: Cache Stats
    await this.runTest('Cache Stats', async () => {
      const stats = goBackendBridge.getCacheStats();
      return stats;
    });

    // Test 4: Metrics Collection
    await this.runTest('Metrics Collection', async () => {
      const metrics = goBackendBridge.getMetrics();
      return metrics;
    });

    // Test 5: Customer List (with caching)
    await this.runTest('Customer List (First Call)', async () => {
      const result = await goBackendBridge.getCustomers({ limit: 5 });
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to get customers');
      }
      return {
        count: result.data?.length || 0,
        fromCache: result.metadata?.fromCache || false,
        latency: result.metadata?.latency,
      };
    });

    // Test 6: Customer List (should be cached)
    await this.runTest('Customer List (Cached Call)', async () => {
      const result = await goBackendBridge.getCustomers({ limit: 5 });
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to get customers');
      }
      return {
        count: result.data?.length || 0,
        fromCache: result.metadata?.fromCache || false,
        latency: result.metadata?.latency,
      };
    });

    // Test 7: Cache Performance Test
    await this.runTest('Cache Performance Test', async () => {
      const iterations = 10;
      const startTime = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        await goBackendBridge.getCustomers({ limit: 5 });
      }
      
      const totalTime = Date.now() - startTime;
      const avgTime = totalTime / iterations;
      
      return {
        iterations,
        totalTime,
        averageTime: avgTime,
      };
    });

    // Test 8: Connection Pool Under Load
    await this.runTest('Connection Pool Load Test', async () => {
      const promises = [];
      const concurrentRequests = 5;
      
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(goBackendBridge.getCustomers({ limit: 1 }));
      }
      
      const results = await Promise.all(promises);
      const successCount = results.filter(r => r.success).length;
      
      return {
        concurrentRequests,
        successCount,
        successRate: successCount / concurrentRequests,
      };
    });

    // Test 9: Cache Invalidation
    await this.runTest('Cache Invalidation', async () => {
      const beforeStats = goBackendBridge.getCacheStats();
      await goBackendBridge.invalidateCachePattern('customers:.*');
      const afterStats = goBackendBridge.getCacheStats();
      
      return {
        beforeSize: beforeStats.size,
        afterSize: afterStats.size,
        invalidated: beforeStats.size - afterStats.size,
      };
    });

    // Test 10: Error Handling
    await this.runTest('Error Handling Test', async () => {
      try {
        // Try to get a non-existent customer
        const result = await goBackendBridge.getCustomer('non-existent-id');
        return {
          handled: !result.success,
          error: result.error?.message,
          hasRetryMetadata: !!result.metadata?.attempt,
        };
      } catch (error) {
        throw new Error('Error was not properly handled by bridge');
      }
    });

    this.printSummary();
  }

  printSummary() {
    console.log('\n📊 Test Summary');
    console.log('================');
    
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    const avgDuration = this.results.reduce((sum, r) => sum + r.duration, 0) / totalTests;
    console.log(`Average Duration: ${avgDuration.toFixed(1)}ms`);
    
    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }

    // Print final stats
    console.log('\n📈 Final System Stats:');
    const finalMetrics = goBackendBridge.getMetrics();
    const finalCacheStats = goBackendBridge.getCacheStats();
    const finalPoolStats = goBackendBridge.getConnectionPoolStats();
    
    console.log(`Requests: ${finalMetrics.requestCount}`);
    console.log(`Errors: ${finalMetrics.errorCount}`);
    console.log(`Error Rate: ${(finalMetrics.errorRate * 100).toFixed(1)}%`);
    console.log(`Avg Latency: ${finalMetrics.averageLatency.toFixed(1)}ms`);
    console.log(`Cache Hit Rate: ${(finalCacheStats.hitRate * 100).toFixed(1)}%`);
    console.log(`Pool Utilization: ${((finalPoolStats.active / finalPoolStats.total) * 100).toFixed(1)}%`);
  }
}

// Run the tests
async function main() {
  const tester = new GoBackendIntegrationTester();
  
  try {
    await tester.runAllTests();
    process.exit(0);
  } catch (error) {
    console.error('Test runner failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

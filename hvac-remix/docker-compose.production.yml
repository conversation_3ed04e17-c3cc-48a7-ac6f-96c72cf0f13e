# HVAC-Remix CRM Production Docker Compose
# Complete production deployment with all services and integrations

version: '3.8'

services:
  # HVAC-Remix Frontend Application
  hvac-remix:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: hvac-remix-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      - DATABASE_URL=postgresql://hvacdb:${POSTGRES_PASSWORD}@postgres:5432/hvacdb
      - REDIS_URL=redis://redis:6379
      - QDRANT_URL=http://qdrant:6333
      - GOBACKEND_URL=http://gobackend-kratos:8080
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - GOBACKEND_HOST=gobackend-kratos
      - GOBACKEND_PORT=8080
      - SESSION_SECRET=${SESSION_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=info
    volumes:
      - hvac-logs:/app/logs
      - hvac-uploads:/app/uploads
      - hvac-tmp:/app/tmp
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      qdrant:
        condition: service_started
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.hvac-remix.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.hvac-remix.tls=true"
      - "traefik.http.routers.hvac-remix.tls.certresolver=letsencrypt"

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hvac-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=hvacdb
      - POSTGRES_USER=hvacdb
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvacdb -d hvacdb"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: hvac-redis
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: hvac-qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant-data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    networks:
      - hvac-network

  # GoBackend-Kratos Integration
  gobackend-kratos:
    image: gobackend-kratos:latest
    container_name: hvac-gobackend
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "8086:8086"
    environment:
      - GO_ENV=production
      - DATABASE_URL=postgresql://hvacdb:${POSTGRES_PASSWORD}@postgres:5432/hvacdb
      - REDIS_URL=redis://redis:6379
      - LM_STUDIO_URL=http://host.docker.internal:1234
      - LOG_LEVEL=info
    volumes:
      - gobackend-logs:/app/logs
      - gobackend-data:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: hvac-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx-logs:/var/log/nginx
      - letsencrypt-certs:/etc/letsencrypt
    depends_on:
      - hvac-remix
    networks:
      - hvac-network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: hvac-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - hvac-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: hvac-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - hvac-network

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  qdrant-data:
    driver: local
  hvac-logs:
    driver: local
  hvac-uploads:
    driver: local
  hvac-tmp:
    driver: local
  gobackend-logs:
    driver: local
  gobackend-data:
    driver: local
  nginx-logs:
    driver: local
  letsencrypt-certs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  hvac-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

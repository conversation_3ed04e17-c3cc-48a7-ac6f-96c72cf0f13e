#!/usr/bin/env node

/**
 * 🧹 HVAC-Remix Backend Services Cleanup Script
 *
 * This script removes redundant backend services that are now handled by GoBackend-Kratos
 * and updates imports to use the GoBackend bridge instead.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Services to remove (handled by GoBackend-Kratos)
const SERVICES_TO_REMOVE = [
  'app/services/db.server.ts',
  'app/services/supabase.server.ts',
  'app/services/database-sync.server.ts',
  'app/services/bielik.server.ts',
  'app/services/bielik-enhanced.server.ts',
  'app/services/qdrant.server.ts',
  'app/services/microsoft-graph.server.ts',
  'app/services/outlook-calendar.server.ts',
  'app/services/payment.server.ts',
  'app/services/mfa.server.ts',
  'app/services/sms.server.ts',
  'app/services/predictive-maintenance.server.ts',
  'app/services/search.supabase.server.ts',
  'app/services/augment-supabase.server.ts',
  'app/services/graphql.server.ts',
  'app/services/ocr/ocr.server.ts',
  'app/services/ocr/file-upload.server.ts',
];

// Models to remove (handled by GoBackend-Kratos)
const MODELS_TO_REMOVE = [
  'app/models/user.server.ts',
  'app/models/note.server.ts',
  'app/models/inventory.server.ts',
  'app/models/metadata.server.ts',
  'app/models/notification.server.ts',
  'app/models/report.server.ts',
  'app/models/admin-settings.server.ts',
  'app/models/custom-fields.server.ts',
  'app/models/user-settings.server.ts',
  'app/models/view-definitions.server.ts',
  'app/models/workflow-definitions.server.ts',
];

// Utils to remove (handled by GoBackend-Kratos)
const UTILS_TO_REMOVE = [
  'app/utils/db.server.ts',
  'app/utils/db-pool.server.ts',
  'app/utils/db-optimization.server.ts',
  'app/utils/email.server.ts',
  'app/utils/redis.server.ts',
  'app/utils/indexing.server.ts',
  'app/utils/supabase.ts',
];

// Database files to remove
const DATABASE_FILES_TO_REMOVE = [
  'prisma/schema.prisma',
  'prisma/sqlite-schema.prisma',
  'prisma/seed.ts',
  'prisma/data.db',
  'prisma/data/sqlite.db',
  'prisma/data/sqlite.db-journal',
  'app/db.server.ts',
  'app/supabase.server.ts',
];

// Scripts to remove (database-related)
const SCRIPTS_TO_REMOVE = [
  'scripts/migrate-data.js',
  'scripts/migrate-data-to-supabase.js',
  'scripts/migrate-to-postgres.js',
  'scripts/setup-supabase.js',
  'scripts/reset-supabase.js',
  'scripts/backup-database.js',
  'scripts/optimize-database.js',
  'scripts/push-schema.js',
];

function removeFile(filePath) {
  const fullPath = path.join(__dirname, filePath);
  if (fs.existsSync(fullPath)) {
    try {
      fs.unlinkSync(fullPath);
      console.log(`✅ Removed: ${filePath}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to remove ${filePath}:`, error.message);
      return false;
    }
  } else {
    console.log(`⚠️  File not found: ${filePath}`);
    return false;
  }
}

function removeDirectory(dirPath) {
  const fullPath = path.join(__dirname, dirPath);
  if (fs.existsSync(fullPath)) {
    try {
      fs.rmSync(fullPath, { recursive: true, force: true });
      console.log(`✅ Removed directory: ${dirPath}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to remove directory ${dirPath}:`, error.message);
      return false;
    }
  } else {
    console.log(`⚠️  Directory not found: ${dirPath}`);
    return false;
  }
}

function updatePackageJsonScripts() {
  const packageJsonPath = path.join(__dirname, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ package.json not found');
    return;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

    // Remove database-related scripts
    const scriptsToRemove = [
      'setup',
      'db:optimize',
      'db:setup',
      'db:migrate',
      'db:reset',
      'backup:db',
      'preproduction',
    ];

    scriptsToRemove.forEach(script => {
      if (packageJson.scripts[script]) {
        delete packageJson.scripts[script];
        console.log(`✅ Removed script: ${script}`);
      }
    });

    // Update build script to remove patch-build.js (if it's database-related)
    if (packageJson.scripts.build && packageJson.scripts.build.includes('patch-build.js')) {
      packageJson.scripts.build = 'remix build';
      console.log('✅ Updated build script');
    }

    // Remove supabase configuration
    if (packageJson.supabase) {
      delete packageJson.supabase;
      console.log('✅ Removed supabase configuration');
    }

    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Updated package.json');
  } catch (error) {
    console.error('❌ Failed to update package.json:', error.message);
  }
}

function main() {
  console.log('🧹 Starting HVAC-Remix Backend Services Cleanup...\n');

  let removedCount = 0;
  let totalCount = 0;

  // Remove services
  console.log('📁 Removing redundant services...');
  SERVICES_TO_REMOVE.forEach(file => {
    totalCount++;
    if (removeFile(file)) removedCount++;
  });

  // Remove models
  console.log('\n📁 Removing redundant models...');
  MODELS_TO_REMOVE.forEach(file => {
    totalCount++;
    if (removeFile(file)) removedCount++;
  });

  // Remove utils
  console.log('\n📁 Removing redundant utils...');
  UTILS_TO_REMOVE.forEach(file => {
    totalCount++;
    if (removeFile(file)) removedCount++;
  });

  // Remove database files
  console.log('\n📁 Removing database files...');
  DATABASE_FILES_TO_REMOVE.forEach(file => {
    totalCount++;
    if (removeFile(file)) removedCount++;
  });

  // Remove scripts
  console.log('\n📁 Removing redundant scripts...');
  SCRIPTS_TO_REMOVE.forEach(file => {
    totalCount++;
    if (removeFile(file)) removedCount++;
  });

  // Remove directories
  console.log('\n📁 Removing redundant directories...');
  const directoriesToRemove = [
    'prisma/migrations',
    'prisma/data',
    'supabase',
    'app/services/ocr',
  ];

  directoriesToRemove.forEach(dir => {
    totalCount++;
    if (removeDirectory(dir)) removedCount++;
  });

  // Update package.json
  console.log('\n📝 Updating package.json...');
  updatePackageJsonScripts();

  console.log(`\n🎉 Cleanup completed!`);
  console.log(`📊 Removed ${removedCount}/${totalCount} items`);
  console.log(`\n✨ HVAC-Remix is now streamlined to use GoBackend-Kratos for all backend operations!`);
  console.log(`\n🔗 Next steps:`);
  console.log(`   1. Update imports in remaining files to use goBackendBridge`);
  console.log(`   2. Remove any remaining database/backend references`);
  console.log(`   3. Test the application with GoBackend-Kratos`);
  console.log(`   4. Run: npm run build`);
}

main();

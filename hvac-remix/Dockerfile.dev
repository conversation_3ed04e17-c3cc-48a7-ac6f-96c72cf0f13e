# HVAC-Remix CRM Development Dockerfile
# Optimized for development with hot reloading and debugging

FROM node:18-alpine AS base

# Install system dependencies for development
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    curl \
    git \
    bash

# Development stage
FROM base AS development

WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 remix

# Copy package files
COPY package*.json ./
COPY yarn.lock* ./

# Install all dependencies (including dev dependencies)
RUN npm ci --legacy-peer-deps && \
    npm cache clean --force

# Copy source code
COPY --chown=remix:nodejs . .

# Generate Prisma client
RUN npx prisma generate

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp /app/uploads && \
    chown -R remix:nodejs /app

# Set development environment
ENV NODE_ENV=development
ENV PORT=3000
ENV HOST=0.0.0.0
ENV CHOKIDAR_USEPOLLING=true

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Switch to non-root user
USER remix

# Expose ports
EXPOSE 3000 3001

# Development command with hot reloading
CMD ["npm", "run", "dev"]

# Labels
LABEL maintainer="HVAC CRM Team"
LABEL version="2.0-dev"
LABEL description="HVAC-Remix CRM Development Container"